# Animancer Base Pose Integration Fix

## Problem Description

The base pose animation in MagicBlend was not working properly when integrated with <PERSON>imancer. The issue was that:

1. **Base pose remained static** - Even when `useAnimancerAsBaseInput = true`, the base pose from the MagicBlendAsset was static and not animated
2. **Poor connection logic** - The connection between <PERSON><PERSON><PERSON>'s animated output and MagicBlend's PoseJob was not properly established
3. **No reconnection handling** - When assets changed, the connections were not properly refreshed

## Root Cause Analysis

The main issues were in the `MagicBlending.cs` file:

1. **SetNewAsset method** - The logic for connecting Animancer's base layer to the PoseJob was flawed
2. **InitializeForAnimancer method** - The initial connection setup was not working correctly
3. **Missing refresh functionality** - No way to refresh connections when the `useAnimancerAsBaseInput` flag changed

## Solution Implemented

### 1. Fixed SetNewAsset Method

**Before:**
```csharp
// Always connected static base pose first, then tried to override
MagicBlendLibrary.ConnectPose(_poseJobPlayable, playableGraph, blendAsset.basePose);

// Then tried to connect Animancer base layer
if (useAnimancerAsBaseInput && _isAnimancerIntegrationActive && _animancerBaseLayerPlayable.IsValid())
{
    // Connection logic was flawed
}
```

**After:**
```csharp
// Handle base pose connection based on whether we're using Animancer as base input
if (useAnimancerAsBaseInput && _isAnimancerIntegrationActive && _animancerBaseLayerPlayable.IsValid())
{
    // Clear existing connections carefully
    if (_poseJobPlayable.GetInputCount() > 0)
    {
        for (int i = 0; i < _poseJobPlayable.GetInputCount(); i++)
        {
            if (_poseJobPlayable.GetInput(i).IsValid())
            {
                var existingInput = _poseJobPlayable.GetInput(i);
                _poseJobPlayable.DisconnectInput(i);
                // Only destroy if it's not the Animancer base layer
                if (existingInput.GetHandle() != _animancerBaseLayerPlayable.GetHandle())
                {
                    existingInput.Destroy();
                }
            }
        }
    }
    
    // Connect Animancer's live base layer
    _poseJobPlayable.SetInputCount(1);
    _poseJobPlayable.ConnectInput(0, _animancerBaseLayerPlayable, 0);
    _poseJobPlayable.SetInputWeight(0, 1f);
}
else
{
    // Use static base pose from the asset
    MagicBlendLibrary.ConnectPose(_poseJobPlayable, playableGraph, blendAsset.basePose);
}
```

### 2. Simplified InitializeForAnimancer Method

**Key Changes:**
- Removed complex dual-connection logic
- Always connect Animancer base layer to LayeringJob for final output
- Store the base layer playable for use in SetNewAsset
- Let SetNewAsset handle the PoseJob connection based on the flag

### 3. Added RefreshAssetConnections Method

```csharp
/// <summary>
/// Forces a refresh of the current asset connections.
/// Call this when useAnimancerAsBaseInput flag changes or when you need to refresh the connections.
/// </summary>
public void RefreshAssetConnections()
{
    if (blendAsset != null && _isInitialized)
    {
        UpdateMagicBlendAsset(blendAsset, false, 0f);
        Debug.Log("[MagicBlending] Refreshed asset connections");
    }
}
```

### 4. Updated ReconnectAnimancerBaseLayer Method

- Improved connection logic to handle both LayeringJob and PoseJob connections
- Better cleanup of existing connections
- Proper handling of the `useAnimancerAsBaseInput` flag

## How It Works Now

### When useAnimancerAsBaseInput = false (Default)
1. MagicBlend uses the static base pose from the MagicBlendAsset
2. Animancer's base layer is connected to LayeringJob for final output
3. PoseJob receives the static base pose via MagicBlendLibrary.ConnectPose

### When useAnimancerAsBaseInput = true (Animated Base)
1. Animancer's base layer is connected to both LayeringJob and PoseJob
2. PoseJob receives live animated data from Animancer instead of static pose
3. MagicBlend's overlay and blending is applied on top of the animated base

## Testing

A new test script `AnimancerBasePoseTest.cs` has been created to verify the integration:

### Test Cases:
1. **Base Animation Only** - Play just the base animation through Animancer
2. **MagicBlend Static Base** - Play MagicBlend with static base pose
3. **MagicBlend Animated Base** - Play MagicBlend with animated base pose
4. **Toggle Flag** - Switch between static and animated base modes
5. **Refresh Connections** - Force refresh of connections

### Usage:
1. Add the `AnimancerBasePoseTest` component to your character
2. Assign the required components and assets
3. Use keyboard keys 1-5 or the GUI buttons to test different scenarios

## Key Benefits

1. **Animated Base Poses** - Base poses now properly animate when using Animancer
2. **Runtime Switching** - Can switch between static and animated base modes at runtime
3. **Proper Cleanup** - Connections are properly managed and cleaned up
4. **Better Debugging** - Added extensive logging for troubleshooting
5. **Flexible Integration** - Works with both static and animated base pose workflows

## Migration Notes

- Existing MagicBlend setups will continue to work as before (static base pose)
- To enable animated base poses, set `useAnimancerAsBaseInput = true` on the MagicBlending component
- Call `RefreshAssetConnections()` after changing the flag to apply changes immediately
- Use the test script to verify your setup is working correctly
