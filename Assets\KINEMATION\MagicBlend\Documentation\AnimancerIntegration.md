# MagicBlend + Animancer Integration

This document explains how to use MagicBlendAsset with <PERSON><PERSON><PERSON> for seamless animation blending.

## Overview

The integration allows you to use MagicBlendAsset directly with Animan<PERSON>'s Play() methods, just like regular AnimationClips. The base and overlay animations of the MagicBlendAsset are automatically played through <PERSON><PERSON><PERSON>'s animation system.

## Quick Start

### Basic Usage

```csharp
using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;

public class MyAnimationController : MonoBehaviour
{
    [SerializeField] private AnimancerComponent _animancer;
    [SerializeField] private MagicBlendAsset _idleBlend;
    
    void Start()
    {
        // Play MagicBlendAsset with Animancer - it's that simple!
        _animancer.Play(_idleBlend, 0.25f);
    }
}
```

### Advanced Usage

```csharp
// Play with custom fade mode
var state = _animancer.Play(_idleBlend, 0.5f, FadeMode.FromStart);

// Play on a specific layer
_animancer.Graph.Layers[1].Play(_idleBlend, 0.3f);

// Create transition first for more control
var transition = _idleBlend.CreateTransition(0.25f);
var state = _animancer.Play(transition);

// Check if current state is a MagicBlend
if (_animancer.States.Current.IsMagicBlendState())
{
    var magicBlendState = _animancer.States.Current.AsMagicBlendState();
    Debug.Log($"Playing: {magicBlendState.Asset.name}");
}
```

## Runtime Modification

You can modify MagicBlendAsset properties at runtime:

```csharp
// Change base pose
_idleBlend.SetBasePose(newIdleClip);

// Change overlay pose
_idleBlend.SetOverlayPose(newOverlayClip);

// Adjust weights
_idleBlend.SetGlobalWeight(0.8f);
_idleBlend.SetOverlaySpeed(1.5f);

// Modify layered blends
_idleBlend.SetLayeredBlend("UpperBody", 0.7f, 0.2f, 0.1f);

// Add override overlays
_idleBlend.SetOverrideOverlay(0, newClip, 1.0f, upperBodyMask);
```

## How It Works

### Architecture

1. **MagicBlendTransition**: Implements `ITransition` to work with Animancer
2. **MagicBlendAnimancerState**: Custom AnimancerState that manages the blending
3. **MagicBlendJob**: Animation job that performs the actual bone blending
4. **Extension Methods**: Provide convenient Play() overloads

### Animation Flow

```
MagicBlendAsset
    ├── Base Pose (AnimationClip) ──┐
    └── Overlay Pose (AnimationClip) ┤
                                     ├── MagicBlendJob ──> Final Animation
    Override Overlays ──────────────┘
```

The MagicBlendJob receives input streams from:
- Base pose playable (input 0)
- Overlay source playable (input 1, which may be a mixer for overrides)

## Setup Requirements

### GameObject Setup

Your GameObject needs:
1. **Animator** component
2. **AnimancerComponent** 
3. **KRigComponent** (for bone mapping)

```csharp
// Example setup
var gameObject = new GameObject("Character");
var animator = gameObject.AddComponent<Animator>();
var animancer = gameObject.AddComponent<AnimancerComponent>();
var kRig = gameObject.AddComponent<KRigComponent>();

animancer.Animator = animator;
```

### MagicBlendAsset Setup

Your MagicBlendAsset needs:
1. **Base Pose**: The primary animation clip
2. **Overlay Pose**: The secondary animation clip to blend
3. **Rig Asset**: KRig asset for bone mapping
4. **Layered Blends**: Optional per-bone blend settings

## Extension Methods Reference

### AnimancerComponent Extensions

```csharp
// Basic play
AnimancerState Play(MagicBlendAsset blendAsset, float fadeTime = 0.25f)

// Play with fade mode
AnimancerState Play(MagicBlendAsset blendAsset, float fadeTime, FadeMode mode)
```

### AnimancerGraph Extensions

```csharp
// Play on default layer (layer 0)
AnimancerState Play(MagicBlendAsset blendAsset, float fadeTime = 0.25f)
```

### AnimancerLayer Extensions

```csharp
// Play on specific layer
AnimancerState Play(MagicBlendAsset blendAsset, float fadeTime = 0.25f)
```

### MagicBlendAsset Extensions

```csharp
// Create transition
MagicBlendTransition CreateTransition(float fadeTime = 0.25f)
```

### AnimancerState Extensions

```csharp
// Check if state is MagicBlend
bool IsMagicBlendState()

// Cast to MagicBlendAnimancerState
MagicBlendAnimancerState AsMagicBlendState()
```

## Runtime Modification Methods

### Basic Properties

```csharp
void SetBasePose(AnimationClip newBasePose)
void SetOverlayPose(AnimationClip newOverlayPose)
void SetOverlaySpeed(float newSpeed)
void SetGlobalWeight(float newWeight)
void SetBlendTime(float newBlendTime)
void SetIsAnimation(bool isAnimated)
```

### Advanced Blending

```csharp
void SetLayeredBlend(string layerName, float baseWeight, float additiveWeight = 0f, float localWeight = 0f)
void SetOverrideOverlay(int index, AnimationClip newOverlay, float weight = 1f, AvatarMask mask = null)
void RemoveOverrideOverlay(int index)
void ClearOverrideOverlays()
```

## Example Scenarios

### Character State Machine

```csharp
public class CharacterController : MonoBehaviour
{
    [SerializeField] private AnimancerComponent _animancer;
    [SerializeField] private MagicBlendAsset _idle, _walk, _run;
    
    public void SetMovementState(float speed)
    {
        if (speed < 0.1f)
            _animancer.Play(_idle, 0.3f);
        else if (speed < 5f)
            _animancer.Play(_walk, 0.3f);
        else
            _animancer.Play(_run, 0.3f);
    }
}
```

### Dynamic Overlay System

```csharp
public class WeaponSystem : MonoBehaviour
{
    [SerializeField] private MagicBlendAsset _combatStance;
    [SerializeField] private AnimationClip[] _weaponOverlays;
    
    public void EquipWeapon(int weaponIndex)
    {
        if (weaponIndex < _weaponOverlays.Length)
        {
            _combatStance.SetOverlayPose(_weaponOverlays[weaponIndex]);
            // The change will be applied automatically on next frame
        }
    }
}
```

## Troubleshooting

### Common Issues

1. **"KRigComponent not found"**: Ensure your GameObject has a KRigComponent
2. **"Bone handles not created"**: Check that KRigComponent has valid transforms
3. **"No animation playing"**: Verify MagicBlendAsset has valid base and overlay poses

### Performance Tips

1. Cache MagicBlendTransitions when possible
2. Use object pooling for frequently created/destroyed states
3. Limit runtime modifications to avoid garbage collection
4. Use appropriate fade durations (shorter = better performance)

## Integration with Existing Systems

This integration is designed to work alongside existing Animancer workflows. You can mix MagicBlendAssets with regular AnimationClips seamlessly:

```csharp
// Mix different animation types
_animancer.Play(regularClip, 0.2f);           // Regular clip
_animancer.Play(magicBlendAsset, 0.3f);       // MagicBlend asset
_animancer.Play(animancerTransition, 0.25f);  // Animancer transition
```
