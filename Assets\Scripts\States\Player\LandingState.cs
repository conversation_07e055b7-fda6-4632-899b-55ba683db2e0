// // Animancer // https://kybernetik.com.au/animancer // Copyright 2018-2023 Kybernetik //
//
// #pragma warning disable CS0649 // Field is never assigned to, and will always have its default value.
//
// using NewAnimancer.Units;
// using Module.Mono.Animancer.RealsticFemale;
// using UnityEngine;
// using UnityEngine.Events;
//
// namespace Animancer.Examples.AnimatorControllers.GameKit
// {
//     /// <summary>A <see cref="CharacterState"/> which plays a "landing on the ground" animation.</summary>
//     /// <example><see href="https://kybernetik.com.au/animancer/docs/examples/animator-controllers/3d-game-kit/landing">3D Game Kit/Landing</see></example>
//     /// https://kybernetik.com.au/animancer/api/Animancer.Examples.AnimatorControllers.GameKit/LandingState
//     /// 
//     public sealed class LandingState : CharacterState
//     {
//         /************************************************************************************************************************/
//
//         [SerializeField] private MixerTransition2D _SoftLanding;
//         [SerializeField] private ClipTransition _HardLanding;
//         [SerializeField, MetersPerSecond] private float _HardLandingForwardSpeed = 5;
//         [SerializeField, MetersPerSecond] private float _HardLandingVerticalSpeed = -10;
//         [SerializeField] private UnityEvent _PlayAudio;// See the Read Me.
//
//         private bool _IsSoftLanding;
//
//         /************************************************************************************************************************/
//
//         private void Awake()
//         {
//             _SoftLanding.Events.OnEnd =
//                 _HardLanding.Events.OnEnd =
//                 () => {};
//         }
//
//         /************************************************************************************************************************/
//
//         public override bool CanEnterState => Character.Parameters.IsGrounded;
//
//         /************************************************************************************************************************/
//
//         /// <summary>
//         /// Performs either a hard or soft landing depending on the current speed (both horizontal and vertical).
//         /// </summary>
//         private void OnEnable()
//         {
//             
//
//             // The landing sounds in the full 3D Game Kit have different variations based on the ground material, just
//             // like the footstep sounds as discussed in the LocomotionState.
//
//             _PlayAudio.Invoke();
//         }
//
//         /************************************************************************************************************************/
//
//         public bool FullMovementControl => _IsSoftLanding;
//
//         public override void OnEnterState(float fadeTime,System.Object conditions = null)
//         {
//             throw new System.NotImplementedException();
//         }
//
//         public override void UpdateState()
//         {
//             
//         }
//
//         /************************************************************************************************************************/
//     }
// }
