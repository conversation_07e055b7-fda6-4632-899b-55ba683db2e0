// Animancer // https://kybernetik.com.au/animancer // Copyright 2018-2023 Kybernetik //

#pragma warning disable CS0649 // Field is never assigned to, and will always have its default value.

using KINEMATION.MagicBlend.Runtime;
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale
{
    public sealed class DynamicLayeredCharacterAnimations : MonoBehaviour
    {
        /************************************************************************************************************************/

        [SerializeField] private LayeredAnimationManager _AnimationManager;
        [SerializeField] private MagicBlendAsset m_currentUpperBodyAnimation;

        [Header("Magic Blend Bone Rebinding")]
        [Tooltip("The root of the original character's skeleton for bone rebinding.")]
        [SerializeField] private Transform _originalRigRoot;

        [Tooltip("The root of the clothing/attachment's skeleton that needs rebinding.")]
        [SerializeField] private Transform _clothingRigRoot;
        
        // This existing header is for runtime blend triggering, not initial bone setup.
        [Header("Magic Blend Integration (Runtime Blending)")]
        [Tooltip("If true, attempts to trigger Magic Blend via LayeredAnimationManager when playing upper body animations.")]
        public bool useMagicBlendIntegration = false;

        [Tooltip("Default blend time for MagicBlend transitions.")]
        [SerializeField] private float defaultBlendTime = 0.25f;

        public void PlayUpperBodyAnimation(MagicBlendAsset blendAsset)
        {
            if (blendAsset == null)
            {
                Debug.LogError("[DynamicLayeredCharacterAnimations] Attempted to play a null MagicBlendAsset.");
                return;
            }

            m_currentUpperBodyAnimation = blendAsset;

            if (_AnimationManager != null)
            {
                // Directly trigger the MagicBlend with the provided asset
                _AnimationManager.TriggerMagicBlend(blendAsset, true, defaultBlendTime);
            }
            else
            {
                Debug.LogWarning("[DynamicLayeredCharacterAnimations] AnimationManager is not assigned. Cannot play animation.");
            }
        }
        
        public void FadeOutUpperBody()
        {
            _AnimationManager.FadeOutUpperBody();
            m_currentUpperBodyAnimation = null;
        }

        /************************************************************************************************************************/
        // Magic Blend Bone Parenting Initialization

        private void Start()
        {
            InitializeMagicBlendBoneRebinding();
        }

        private void InitializeMagicBlendBoneRebinding()
        {
            if (_AnimationManager == null)
            {
                Debug.LogWarning("[DynamicLayeredCharacterAnimations] LayeredAnimationManager is not assigned. Cannot perform Magic Blend bone rebinding.");
                return;
            }

            // Check the public flag in LayeredAnimationManager
            if (_AnimationManager.enableMagicBlendBoneRebinding) 
            {
                if (_originalRigRoot == null || _clothingRigRoot == null)
                {
                    Debug.LogWarning("[DynamicLayeredCharacterAnimations] Original rig root or clothing rig root is not assigned. Cannot perform Magic Blend bone rebinding.");
                    return;
                }
                PerformRebinding(_clothingRigRoot, _originalRigRoot);
            }
        }

        private void PerformRebinding(Transform currentClothingRig, Transform targetOriginalRig)
        {
            if (currentClothingRig == null || targetOriginalRig == null) return;

            Debug.Log($"[DynamicLayeredCharacterAnimations] Attempting to rebind bones from '{currentClothingRig.name}' to '{targetOriginalRig.name}'. Check LayeredAnimationManager for enableMagicBlendBoneRebinding flag status: {_AnimationManager.enableMagicBlendBoneRebinding}");

            // Iterate through all child transforms of the clothing rig and attempt to rebind them.
            // This is a general approach. If your clothing items are structured with SkinnedMeshRenderers,
            // you might prefer to iterate through smr.bones for a more targeted rebinding.
            // Ensure that the LayeredAnimationManager.SearchAndRebind method is appropriate for your hierarchy.
            foreach (Transform bone in currentClothingRig.GetComponentsInChildren<Transform>(true))
            {
                // Avoid rebinding the root of the clothing rig itself, or other special cases.
                if (bone == currentClothingRig) continue; 
                
                // Optional: Add more sophisticated checks if 'bone' should be processed, e.g., based on name or tag.
                LayeredAnimationManager.SearchAndRebind(bone, targetOriginalRig);
            }
            
            Debug.Log($"[DynamicLayeredCharacterAnimations] Bone rebinding process completed for '{currentClothingRig.name}'.");
        }

        /************************************************************************************************************************/
    }
}
