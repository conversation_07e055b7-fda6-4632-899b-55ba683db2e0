using System.Collections;
using Module.Mono;
using UnityEngine;
using RootMotion.Demos;
using RootMotion.Dynamics;
using RootMotion.FinalIK;
using KINEMATION.MagicBlend.Runtime;
using Unity.Mathematics;

namespace RootMotion.Demos
{
    public class RagdollAimingController : MonoBehaviour
    {
        public PuppetMaster puppetMaster;
        public AimIK aimIKBeforePhysics;
        public Transform target;

        [Header("Magic Blending")]
        public MagicBlending magicBlending;
        public MagicBlendAsset defaultBlendAsset;
        public MagicBlendAsset aimingBlendAsset;

        [Header("Cosmetics")] public bool fixAiming = true;
        public bool fixLeftHand = true;
        public AimIK aimIKAfterPhysics;
        public bool hasLeftHandIK;
        public LimbIK leftHandIK;
        public Transform leftHandTarget;

        [Header("Aim Controller Settings")] [Range(0f, 1f)]
        public float weight = 1f;

        public float targetSwitchSmoothTime = 0.3f;
        public float weightSmoothTime = 0.3f;
        public bool smoothTurnTowardsTarget = true;
        public float maxRadiansDelta = 3f;
        public float maxMagnitudeDelta = 3f;
        public float slerpSpeed = 3f;
        public float smoothDampTime = 0f;
        public Vector3 pivotOffsetFromRoot = Vector3.up;
        public float minDistance = 1f;
        public Vector3 offset;
        public float maxRootAngle = 45f;
        public bool turnToTarget;
        public float turnToTargetTime = 0.2f;
        public bool useAnimatedAimDirection;
        public Vector3 animatedAimDirection = Vector3.forward;

        private Transform lastTarget;
        private float switchWeight, switchWeightV;
        private float weightV;
        private Vector3 lastPosition;
        private Vector3 dir;
        private bool lastSmoothTowardsTarget;
        private bool turningToTarget;
        private float turnToTargetMlp = 1f;
        private float turnToTargetMlpV;

        private MagicBlendAsset _currentBlendAsset;

        [SerializeField] private AimingData m_currentAimingData;
        [SerializeField] private Animator animator;

        void Start()
        {
            // Register to get some calls from PuppetMaster
            puppetMaster.OnWrite += OnPuppetMasterWrite;

            // Make the other AimIK and left hand IK update after PuppetMaster writes for cosmetic non-physical fixes
            aimIKAfterPhysics.enabled = false;
            leftHandIK.enabled = false;

            lastPosition = aimIKBeforePhysics.solver.IKPosition;
            dir = aimIKBeforePhysics.solver.IKPosition - pivot;

            aimIKBeforePhysics.solver.target = null;
        }

        // void OnEnable()
        // {
        //     aimIKBeforePhysics.solver.IKPositionWeight = 1;
        //     aimIKAfterPhysics.solver.IKPositionWeight = 1;
        //     leftHandIK.solver.IKPositionWeight = 1;
        //     leftHandIK.solver.IKRotationWeight = 1;
        // }

        void OnDisable()
        {
            aimIKBeforePhysics.solver.IKPositionWeight = 0;
            aimIKBeforePhysics.solver.clampWeight = 0;
            aimIKBeforePhysics.solver.poleWeight = 0;
            aimIKAfterPhysics.solver.IKPositionWeight = 0;
            if (hasLeftHandIK)
            {
                leftHandIK.solver.IKPositionWeight = 0;
                leftHandIK.solver.IKRotationWeight = 0;
            }
        }

        void LateUpdate()
        {
            // If target has changed...
            if (target != lastTarget)
            {
                if (lastTarget == null && target != null && aimIKBeforePhysics.solver.IKPositionWeight <= 0f)
                {
                    lastPosition = target.position;
                    dir = target.position - pivot;
                    aimIKBeforePhysics.solver.IKPosition = target.position + offset;
                }
                else
                {
                    lastPosition = aimIKBeforePhysics.solver.IKPosition;
                    dir = aimIKBeforePhysics.solver.IKPosition - pivot;
                }

                switchWeight = 0f;
                lastTarget = target;
            }

            // Smooth weight
            float targetWeight = target != null ? weight : 0f;
            aimIKBeforePhysics.solver.IKPositionWeight = Mathf.SmoothDamp(aimIKBeforePhysics.solver.IKPositionWeight,
                targetWeight, ref weightV, weightSmoothTime);
            aimIKAfterPhysics.solver.IKPositionWeight = aimIKBeforePhysics.solver.IKPositionWeight;
            if (hasLeftHandIK)
            {
                leftHandIK.solver.IKPositionWeight = aimIKBeforePhysics.solver.IKPositionWeight;
                leftHandIK.solver.IKRotationWeight = aimIKBeforePhysics.solver.IKPositionWeight;
            }

            if (aimIKBeforePhysics.solver.IKPositionWeight >= 0.999f &&
                targetWeight > aimIKBeforePhysics.solver.IKPositionWeight)
                aimIKBeforePhysics.solver.IKPositionWeight = 1f;
            if (aimIKBeforePhysics.solver.IKPositionWeight <= 0.001f &&
                targetWeight < aimIKBeforePhysics.solver.IKPositionWeight)
                aimIKBeforePhysics.solver.IKPositionWeight = 0f;

            //if (aimIKBeforePhysics.solver.IKPositionWeight <= 0f) return;

            // Smooth target switching
            switchWeight = Mathf.SmoothDamp(switchWeight, 1f, ref switchWeightV, targetSwitchSmoothTime);
            if (switchWeight >= 0.999f) switchWeight = 1f;

            if (target != null)
            {
                aimIKBeforePhysics.solver.IKPosition =
                    Vector3.Lerp(lastPosition, target.position + offset, switchWeight);
            }

            // Smooth turn towards target
            if (smoothTurnTowardsTarget != lastSmoothTowardsTarget)
            {
                dir = aimIKBeforePhysics.solver.IKPosition - pivot;
                lastSmoothTowardsTarget = smoothTurnTowardsTarget;
            }

            if (smoothTurnTowardsTarget)
            {
                Vector3 targetDir = aimIKBeforePhysics.solver.IKPosition - pivot;

                // Slerp
                if (slerpSpeed > 0f) dir = Vector3.Slerp(dir, targetDir, Time.deltaTime * slerpSpeed);

                // RotateTowards
                if (maxRadiansDelta > 0 || maxMagnitudeDelta > 0f)
                    dir = Vector3.RotateTowards(dir, targetDir, Time.deltaTime * maxRadiansDelta, maxMagnitudeDelta);

                // SmoothDamp
                if (smoothDampTime > 0f)
                {
                    float yaw = V3Tools.GetYaw(dir);
                    float targetYaw = V3Tools.GetYaw(targetDir);
                    float y = Mathf.SmoothDampAngle(yaw, targetYaw, ref yawV, smoothDampTime);

                    float pitch = V3Tools.GetPitch(dir);
                    float targetPitch = V3Tools.GetPitch(targetDir);
                    float p = Mathf.SmoothDampAngle(pitch, targetPitch, ref pitchV, smoothDampTime);

                    float dirMag = Mathf.SmoothDamp(dir.magnitude, targetDir.magnitude, ref dirMagV, smoothDampTime);

                    dir = Quaternion.Euler(p, y, 0f) * Vector3.forward * dirMag;
                }

                aimIKBeforePhysics.solver.IKPosition = pivot + dir;
            }

            // Min distance from the pivot
            ApplyMinDistance();

            // Root rotation
            RootRotation();

            // Offset mode
            if (useAnimatedAimDirection)
            {
                aimIKBeforePhysics.solver.axis =
                    aimIKBeforePhysics.solver.transform.InverseTransformVector(aimIKBeforePhysics.transform.rotation *
                                                                               animatedAimDirection);
            }

            // Set the animator Pistol layer weight to 1
            if (animator != null)
            {
                //animator.SetLayerWeight(animator.GetLayerIndex("Pistol"), aimIKBeforePhysics.solver.IKPositionWeight);
                // if(targetWeight == 0)
                //     animator.SetBool("PistolAim",false);
                // else if(targetWeight == 1)
                //     animator.SetBool("PistolAim",true);
            }
    
            if (magicBlending != null)
            {
                // Determine if we're aiming based on input
                bool isAiming = Input.GetMouseButton(1) && target != null;
                
                // Determine the target asset based on aiming state
                MagicBlendAsset targetAsset = isAiming ? aimingBlendAsset : defaultBlendAsset;
    
                // If the target asset has changed, update MagicBlending
                if (targetAsset != null && _currentBlendAsset != targetAsset)
                {
                    magicBlending.UpdateMagicBlendAsset(targetAsset, true, 0.2f);
                    _currentBlendAsset = targetAsset;
                }
    
                // Control the external weight based on the aim IK weight
                //magicBlending.externalWeight = aimIKBeforePhysics.solver.IKPositionWeight;
            }
        }

        void OnPuppetMasterWrite()
        {
            if (fixAiming && target != null)
            {
                aimIKAfterPhysics.solver.IKPosition = target.position;
                aimIKAfterPhysics.solver.Update();
            }

            if (fixLeftHand && hasLeftHandIK)
            {
                leftHandIK.solver.IKPosition = leftHandTarget.position;
                leftHandIK.solver.IKRotation = leftHandTarget.rotation;
                leftHandIK.solver.Update();
            }
        }

        void OnDestroy()
        {
            if (puppetMaster != null)
            {
                puppetMaster.OnWrite -= OnPuppetMasterWrite;
            }
        }

        void FixedUpdate()
        {
            foreach (Muscle m in puppetMaster.muscles)
            {
                if (m.rigidbody.IsSleeping()) m.rigidbody.WakeUp();
            }
        }

        private float yawV, pitchV, dirMagV;

        // Pivot of rotating the aiming direction.
        private Vector3 pivot
        {
            get
            {
                return aimIKBeforePhysics.transform.position +
                       aimIKBeforePhysics.transform.rotation * pivotOffsetFromRoot;
            }
        }

        // Make sure aiming target is not too close (might make the solver instable when the target is closer to the first bone than the last bone is).
        void ApplyMinDistance()
        {
            Vector3 aimFrom = pivot;
            Vector3 direction = (aimIKBeforePhysics.solver.IKPosition - aimFrom);
            direction = direction.normalized * Mathf.Max(direction.magnitude, minDistance);

            aimIKBeforePhysics.solver.IKPosition = aimFrom + direction;
        }

        // Character root will be rotate around the Y axis to keep root forward within this angle from the aiming direction.
        private void RootRotation()
        {
            float max = Mathf.Lerp(180f, maxRootAngle * turnToTargetMlp, aimIKBeforePhysics.solver.IKPositionWeight);

            if (max < 180f)
            {
                Vector3 faceDirLocal = Quaternion.Inverse(aimIKBeforePhysics.transform.rotation) *
                                       (aimIKBeforePhysics.solver.IKPosition - pivot);
                float angle = Mathf.Atan2(faceDirLocal.x, faceDirLocal.z) * Mathf.Rad2Deg;

                float rotation = 0f;

                if (angle > max)
                {
                    rotation = angle - max;
                    if (!turningToTarget && turnToTarget) StartCoroutine(TurnToTarget());
                }

                if (angle < -max)
                {
                    rotation = angle + max;
                    if (!turningToTarget && turnToTarget) StartCoroutine(TurnToTarget());
                }

                aimIKBeforePhysics.transform.rotation =
                    Quaternion.AngleAxis(rotation, aimIKBeforePhysics.transform.up) *
                    aimIKBeforePhysics.transform.rotation;
            }
        }

        // Aligns the root forward to target direction after "Max Root Angle" has been exceeded.
        private IEnumerator TurnToTarget()
        {
            turningToTarget = true;

            while (turnToTargetMlp > 0f)
            {
                turnToTargetMlp = Mathf.SmoothDamp(turnToTargetMlp, 0f, ref turnToTargetMlpV, turnToTargetTime);
                if (turnToTargetMlp < 0.01f) turnToTargetMlp = 0f;

                yield return null;
            }

            turnToTargetMlp = 1f;
            turningToTarget = false;
        }

        public void SetWeaponData(AimingData aimingData)
        {
            // minDistance = aimingData.minDistance;
            // maxRootAngle = aimingData.maxRootAngle;
            // turnToTargetTime = aimingData.turnToTargetTime;
            weight = 0;
            aimIKBeforePhysics.solver.transform = aimingData.AimTarget;
            aimIKAfterPhysics.solver.transform = aimingData.AimTarget;
            if (aimingData.HasLeftHandIK)
                aimingData.LeftHandFixTransform = aimingData.LeftHandFixTransform;
            weight = 1;
            m_currentAimingData = aimingData;
        }

        public float3 GetAimTarget()
        {
            return m_currentAimingData.AimTarget.position;
        }

        public Transform GetPointer()
        {
            return m_currentAimingData.Pointer;
        }
    }
}