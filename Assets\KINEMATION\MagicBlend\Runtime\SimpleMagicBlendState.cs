using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Animations;

namespace MagicBlendAnimancerIntegration
{
    /// <summary>
    /// Simple MagicBlend state that just plays base and overlay poses without complex blending
    /// This is a fallback implementation that avoids the complex job system
    /// </summary>
    public sealed class SimpleMagicBlendState : AnimancerState
    {
        private readonly MagicBlendAsset _asset;
        private bool _isInitialized;

        public SimpleMagicBlendState(MagicBlendAsset asset)
        {
            _asset = asset ?? throw new System.ArgumentNullException(nameof(asset));
        }

        /// <summary>Gets the MagicBlendAsset associated with this state.</summary>
        public MagicBlendAsset Asset => _asset;

        /************************************************************************************************************************/
        /************************************************************************************************************************/

        public new object Key => _asset;
        public override float Length => _asset?.basePose?.length ?? 0f;
        public override bool IsLooping => _asset?.basePose?.isLooping ?? false;
        public override Object MainObject => _asset;


        protected override void CreatePlayable(out Playable playable)
        {
            if (_isInitialized)
            {
                playable = Playable;
                return;
            }

            if (_asset == null)
            {
                Debug.LogError("[SimpleMagicBlendState] Cannot create playable, MagicBlendAsset is null.");
                playable = Playable.Null;
                return;
            }

            var graph = Graph.PlayableGraph;
            if (!graph.IsValid())
            {
                Debug.LogError("[SimpleMagicBlendState] PlayableGraph is not valid.");
                playable = Playable.Null;
                return;
            }

            // Create a simple animation mixer that will play the base pose directly
            var mixer = AnimationMixerPlayable.Create(graph, 2);
            
            // Connect base pose directly to mixer
            if (_asset.basePose != null)
            {
                var basePosePlayable = AnimationClipPlayable.Create(graph, _asset.basePose);
                basePosePlayable.SetApplyFootIK(false);
                mixer.ConnectInput(0, basePosePlayable, 0, 1f);
                Debug.Log($"[SimpleMagicBlendState] Connected base pose: {_asset.basePose.name}");
            }
            else
            {
                Debug.LogWarning($"[SimpleMagicBlendState] No base pose found in asset: {_asset.name}");
            }
            
            // Connect overlay pose if it exists
            if (_asset.overlayPose != null)
            {
                var overlayPosePlayable = AnimationClipPlayable.Create(graph, _asset.overlayPose);
                overlayPosePlayable.SetApplyFootIK(false);
                overlayPosePlayable.SetSpeed(_asset.isAnimation ? _asset.overlaySpeed : 0f);
                mixer.ConnectInput(1, overlayPosePlayable, 0, _asset.globalWeight);
                Debug.Log($"[SimpleMagicBlendState] Connected overlay pose: {_asset.overlayPose.name} with weight: {_asset.globalWeight}");
            }

            playable = mixer;
            _isInitialized = true;
            
            Debug.Log($"[SimpleMagicBlendState] Created simple playable for asset: {_asset.name}");
        }

        /************************************************************************************************************************/
        /************************************************************************************************************************/

        protected override void OnSetIsPlaying()
        {
            base.OnSetIsPlaying();
            
            // Update playable speeds based on playing state
            if (Playable.IsValid())
            {
                var mixer = (AnimationMixerPlayable)Playable;
                
                // Set base pose speed
                if (mixer.GetInputCount() > 0 && mixer.GetInput(0).IsValid())
                {
                    mixer.GetInput(0).SetSpeed(IsPlaying ? 1f : 0f);
                }
                
                // Set overlay pose speed
                if (mixer.GetInputCount() > 1 && mixer.GetInput(1).IsValid())
                {
                    var overlaySpeed = IsPlaying ? (_asset.isAnimation ? _asset.overlaySpeed : 0f) : 0f;
                    mixer.GetInput(1).SetSpeed(overlaySpeed);
                }
            }
        }

        /************************************************************************************************************************/
        /************************************************************************************************************************/

        /// <summary>
        /// Updates the playable when the asset changes at runtime
        /// </summary>
        public void UpdateAsset()
        {
            if (!Playable.IsValid() || !_isInitialized)
                return;

            var mixer = (AnimationMixerPlayable)Playable;
            
            // Disconnect existing inputs
            for (int i = 0; i < mixer.GetInputCount(); i++)
            {
                if (mixer.GetInput(i).IsValid())
                {
                    Graph.PlayableGraph.DestroyPlayable(mixer.GetInput(i));
                }
                mixer.DisconnectInput(i);
            }
            
            // Reconnect with updated asset
            if (_asset.basePose != null)
            {
                var basePosePlayable = AnimationClipPlayable.Create(Graph.PlayableGraph, _asset.basePose);
                basePosePlayable.SetApplyFootIK(false);
                mixer.ConnectInput(0, basePosePlayable, 0, 1f);
                Debug.Log($"[SimpleMagicBlendState] Updated base pose: {_asset.basePose.name}");
            }
            
            if (_asset.overlayPose != null)
            {
                var overlayPosePlayable = AnimationClipPlayable.Create(Graph.PlayableGraph, _asset.overlayPose);
                overlayPosePlayable.SetApplyFootIK(false);
                overlayPosePlayable.SetSpeed(_asset.isAnimation ? _asset.overlaySpeed : 0f);
                mixer.ConnectInput(1, overlayPosePlayable, 0, _asset.globalWeight);
                Debug.Log($"[SimpleMagicBlendState] Updated overlay pose: {_asset.overlayPose.name}");
            }
        }

        /************************************************************************************************************************/
        /************************************************************************************************************************/

        public override AnimancerState Clone(CloneContext context) => new SimpleMagicBlendState(_asset);

        /************************************************************************************************************************/
    }

    /// <summary>
    /// Simple transition for MagicBlend assets that uses the SimpleMagicBlendState
    /// </summary>
    public sealed class SimpleMagicBlendTransition : ITransition
    {
        private readonly MagicBlendAsset _asset;
        private readonly float _fadeDuration;

        public SimpleMagicBlendTransition(MagicBlendAsset asset, float fadeDuration = 0.25f)
        {
            _asset = asset ?? throw new System.ArgumentNullException(nameof(asset));
            _fadeDuration = fadeDuration;
        }

        public object Key => _asset;
        public float FadeDuration => _fadeDuration;
        public FadeMode FadeMode => FadeMode.FixedSpeed;

        public AnimancerState CreateState() => new SimpleMagicBlendState(_asset);
        public void Apply(AnimancerState state) { }
    }
}
