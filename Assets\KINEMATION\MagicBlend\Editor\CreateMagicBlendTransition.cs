#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using MagicBlendAnimancerIntegration;

public static class CreateMagicBlendTransition
{
    [MenuItem("Tools/Create MagicBlend Transition Asset")]
    public static void CreateTransitionAsset()
    {
        // Create the asset directly
        var asset = ScriptableObject.CreateInstance<MagicBlendTransition>();
        
        // Save it to Assets folder
        string path = "Assets/MagicBlendTransition.asset";
        AssetDatabase.CreateAsset(asset, path);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        
        // Select it
        Selection.activeObject = asset;
        EditorUtility.FocusProjectWindow();
        
        Debug.Log("✅ MagicBlendTransition asset created successfully at: " + path);
    }
}
#endif
