# MagicBlend + Animancer Base Pose Fix - FINAL SOLUTION

## The Real Core Issue (Based on Community Feedback)

After analyzing the Discord chat thread, the real issue is **bone indexing misalignment** when using custom playable graphs:

1. **MagicBlend was designed for Animator Controllers** - It creates stream handles bound to the Animator's default graph
2. **Custom playable graphs (<PERSON><PERSON><PERSON>, Motion Matching) have different bone indexing** - The bone indices don't align
3. **This causes wrong bones to be affected** - Arms affecting legs, incorrect rotations, etc.
4. **Missing bones in blend chains** - Twist bones and other bones were missing from the layer configurations

## Secondary Issues

- **PoseJob not configured to read animated input stream**
- **Poor connection logic between Animancer and MagicBlend**
- **No validation of bone hierarchy**

## Key Fix: Job Configuration

The critical fix was ensuring that when `useAnimancerAsBaseInput = true`, the PoseJob is properly configured:

```csharp
// CRITICAL: Enable pose reading so PoseJob processes the animated input
_poseJob.readPose = true;
_poseJob.alwaysAnimate = true;
_poseJobPlayable.SetJobData(_poseJob);
```

## How the Jobs Work

1. **PoseJob** - Reads the base pose from the animation stream and stores it in `atom.activePose.basePose`
2. **OverlayJob** - Reads the overlay pose and stores it in `atom.activePose.overlayPose`
3. **LayeringJob** - Blends the base and overlay poses together

The PoseJob has two key flags:
- `readPose` - Whether to read pose data from the input stream
- `alwaysAnimate` - Whether to always process animation data

## Complete Solution

### 1. Custom Graph Atom Setup
```csharp
// NEW: Setup atoms specifically for custom playable graphs
private NativeArray<BlendStreamAtom> SetupBlendAtomsForCustomGraph(Animator animator, KRigComponent rigComponent, PlayableGraph customGraph)
{
    var bones = rigComponent.GetRigTransforms();
    int num = bones.Length;
    var blendAtoms = new NativeArray<BlendStreamAtom>(num, Allocator.Persistent);

    for (int i = 0; i < num; i++)
    {
        Transform bone = bones[i];
        if (bone == null)
        {
            Debug.LogWarning($"Null bone at index {i} in rig hierarchy");
            continue;
        }

        // Bind stream transform to ensure correct bone indexing
        var streamHandle = animator.BindStreamTransform(bone);
        blendAtoms[i] = new BlendStreamAtom() { handle = streamHandle };
    }

    return blendAtoms;
}
```

### 2. Bone Hierarchy Validation
```csharp
// NEW: Validate bone hierarchy and blend asset configuration
public void ValidateBoneHierarchy()
{
    // Check for null bones in hierarchy
    // Validate blend asset bone chains
    // Report missing bones that could cause indexing issues
}
```

### 3. Rebuild Blend Atoms
```csharp
// NEW: Rebuild atoms when graph or hierarchy changes
public void RebuildBlendAtoms()
{
    if (_atoms.IsCreated) _atoms.Dispose();

    if (_isAnimancerIntegrationActive && playableGraph.IsValid())
    {
        _atoms = SetupBlendAtomsForCustomGraph(_animator, _rigComponent, playableGraph);
    }
    else
    {
        _atoms = MagicBlendLibrary.SetupBlendAtoms(_animator, _rigComponent);
    }

    // Update all job data with new atoms
    _poseJob.atoms = _atoms;
    _overlayJob.atoms = _atoms;
    _layeringJob.atoms = _atoms;
}
```

### 4. SetNewAsset Method
```csharp
if (useAnimancerAsBaseInput && _isAnimancerIntegrationActive && _animancerBaseLayerPlayable.IsValid())
{
    // Connect Animancer's live base layer to PoseJob
    _poseJobPlayable.SetInputCount(1);
    _poseJobPlayable.ConnectInput(0, _animancerBaseLayerPlayable, 0);
    _poseJobPlayable.SetInputWeight(0, 1f);
    
    // CRITICAL: Enable pose reading
    _poseJob.readPose = true;
    _poseJob.alwaysAnimate = true;
    _poseJobPlayable.SetJobData(_poseJob);
}
else
{
    // Use static base pose
    MagicBlendLibrary.ConnectPose(_poseJobPlayable, playableGraph, blendAsset.basePose);
    
    // Disable continuous reading for static poses
    _poseJob.readPose = false;
    _poseJob.alwaysAnimate = alwaysAnimatePoses;
    _poseJobPlayable.SetJobData(_poseJob);
}
```

### 2. InitializeJobs Method
```csharp
_poseJob = new PoseJob()
{
    atoms = _atoms,
    root = rootSceneHandle,
    alwaysAnimate = alwaysAnimatePoses,
    readPose = useAnimancerAsBaseInput // Start with correct setting
};
```

### 3. RefreshAssetConnections Method
```csharp
public void RefreshAssetConnections()
{
    if (blendAsset != null && _isInitialized)
    {
        // Ensure we have the latest Animancer base layer reference
        if (useAnimancerAsBaseInput && _isAnimancerIntegrationActive)
        {
            if (TryGetComponent<HybridAnimancerComponent>(out var animancer) && animancer.Layers.Count > 0)
            {
                _animancerBaseLayerPlayable = animancer.Layers[0].Playable;
            }
        }
        
        UpdateMagicBlendAsset(blendAsset, false, 0f);
    }
}
```

## Usage Instructions

1. **Set the flag**: `magicBlending.useAnimancerAsBaseInput = true`
2. **Play base animation**: `animancer.Play(walkAnimation)`
3. **Play MagicBlend overlay**: `animancer.Play(magicBlendAsset)`
4. **Refresh if needed**: `magicBlending.RefreshAssetConnections()`

## Critical Steps for Custom Playable Graphs

### For Motion Matching / Animancer Integration:

1. **Initialize MagicBlend AFTER setting up your custom graph**
2. **Use the custom graph in InitializeForAnimancer**:
   ```csharp
   playableGraph = animancer.playableGraph; // Use Animancer's graph
   _atoms = SetupBlendAtomsForCustomGraph(_animator, _rigComponent, playableGraph);
   ```

3. **Validate your bone chains** - Ensure all bones (including twist bones) are included:
   ```csharp
   magicBlending.ValidateBoneHierarchy(); // Check for missing bones
   ```

4. **Rebuild atoms if hierarchy changes**:
   ```csharp
   magicBlending.RebuildBlendAtoms(); // After dynamic bone changes
   ```

## Testing Tools Created

1. **AnimancerBasePoseTest.cs** - Comprehensive test with 7 scenarios
2. **MagicBlendConnectionDebugger.cs** - Debug tool to inspect connections
3. **AnimancerMagicBlendUsageExample.cs** - Practical usage example

## Debug Commands

- **1-5** - Original test scenarios
- **6** - Validate bone hierarchy (NEW)
- **7** - Rebuild blend atoms (NEW)
- **F1** - Log connection details
- **F2** - Log playable graph structure

## Expected Result

When working correctly:
- Base animations (walk, run, idle) play through Animancer
- MagicBlend overlays (aiming, reloading) are applied on top
- The base pose is **animated** (not static) when `useAnimancerAsBaseInput = true`
- Smooth transitions between different base animations while maintaining overlays

## Troubleshooting Common Issues

### Issue: Arms affecting legs, wrong bone rotations
**Cause**: Bone indexing misalignment between custom graph and MagicBlend
**Solution**:
1. Call `ValidateBoneHierarchy()` to check for missing bones
2. Ensure all twist bones and intermediate bones are in your blend chains
3. Call `RebuildBlendAtoms()` after any hierarchy changes

### Issue: Base pose remains static
**Cause**: PoseJob not reading animated input
**Solution**:
1. Set `useAnimancerAsBaseInput = true`
2. Ensure `_poseJob.readPose = true` and `_poseJob.alwaysAnimate = true`
3. Call `RefreshAssetConnections()` after changing settings

### Issue: MagicBlend not working with Motion Matching
**Cause**: Motion Matching uses custom playable graph, not Animator Controller
**Solution**:
1. Link MagicBlend to the Motion Matching playable graph
2. Initialize MagicBlend AFTER Motion Matching setup
3. Use `SetupBlendAtomsForCustomGraph()` for proper bone indexing

The fix ensures proper bone indexing alignment between custom playable graphs and MagicBlend, while also enabling animated base poses with overlays applied on top.
