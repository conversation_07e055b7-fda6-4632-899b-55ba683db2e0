﻿using System;
using System.Collections.Generic;
using NewAnimancer.Examples.AnimatorControllers.GameKit;
using Module.Mono.Modules;
using NewAnimancer;
using UnityEngine;


namespace Module.Mono.Animancer.RealsticFemale
{
    public abstract class  CharacterState :State
    {
        [SerializeField] public AnimationModuleWithStates<CharacterState,MovementSubState> _Character;
        
        [SerializeField] public Dictionary<string,ClipTransition> ConditionalAnimations;
        public AnimationModuleWithStates<CharacterState,MovementSubState> Character => _Character;
        /************************************************************************************************************************/
    }
}