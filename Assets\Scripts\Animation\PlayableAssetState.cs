﻿// Animancer // https://kybernetik.com.au/animancer // Copyright 2018-2023 Kybernetik //

using NewAnimancer;
using UnityEngine;
using UnityEngine.Playables;
using AnimancerState = NewAnimancer.AnimancerState;

/// <summary>
/// An <see cref="AnimancerState"/> which can be used to play a raw <see cref="Playable"/>.
/// </summary>
public sealed class PlayableAssetState : AnimancerState
{
    private Playable _InputPlayable;

    /// <summary>The <see cref="UnityEngine.Playables.Playable"/> which this state will play.</summary>
    public Playable InputPlayable
    {
        get => _InputPlayable;
        set
        {
            // If this state's own playable is invalid, we can't do graph operations.
            // Just store the target input and return.
            if (!Playable.IsValid())
            {
                _InputPlayable = value;
                return;
            }

            // Check if the new value is already the current input.
            Playable currentInput = Playable.Null;
            if (Playable.GetInputCount() > 0)
            {
                currentInput = Playable.GetInput(0);
            }

            // If the 'value' is the same as 'currentInput' (comparing underlying handles),
            // then no change is needed.
            if (currentInput.GetHandle() == value.GetHandle())
            {
                _InputPlayable = value; // Ensure _InputPlayable field is current
                return;
            }

            // Input is different or needs to be (re)established.
            // Disconnect whatever is currently connected to this.Playable's input port 0.
            if (Playable.GetInputCount() > 0)
            {
                Playable.DisconnectInput(0);
            }

            _InputPlayable = value; // Store the new intended input

            if (_InputPlayable.IsValid()) // If the new input is valid
            {
                Playable.SetInputCount(1); // Ensure this.Playable has one input port.
                Graph.PlayableGraph.Connect(_InputPlayable, 0, Playable, 0); // Connect new_input -> this.Playable
                Playable.SetInputWeight(0, 1f);
            }
            else // New input is invalid
            {
                Playable.SetInputCount(0); // No inputs for this.Playable
            }
        }
    }

    public override AnimationClip Clip => null;
    public override float Length => 1f; 
    public override bool IsLooping => false;

    protected override void CreatePlayable(out Playable playable)
    {
        // Create a playable with zero inputs initially.
        // The InputPlayable setter will manage SetInputCount.
        playable = Playable.Create(Graph, 0);
    }
    
    protected override void OnStartFade()
    {
        // When fading starts, connect the input playable.
        // This ensures the connection is fresh when the state becomes active.
        InputPlayable = _InputPlayable;
    }

    public override AnimancerState Clone(CloneContext context)
    {
        var clone = new PlayableAssetState();
        clone.InputPlayable = InputPlayable;
        return clone;
    }
}
