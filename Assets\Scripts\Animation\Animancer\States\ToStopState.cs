// Animancer // https://kybernetik.com.au/animancer // Copyright 2018-2023 Kybernetik //

#pragma warning disable CS0649 // Field is never assigned to, and will always have its default value.

using System;
using Events;
using Module.Mono.Animancer.RealsticFemale;
using NewAnimancer;
using NewAnimancer.Units;
using UnityEngine;
using Object = System.Object;


namespace NewAnimancer.Examples.AnimatorControllers.GameKit
{
    /// <summary>
    /// A <see cref="CharacterState"/> which keeps the character standing still and occasionally plays alternate
    /// animations if it remains active for long enough.
    /// </summary>
    /// <example><see href="https://kybernetik.com.au/animancer/docs/examples/animator-controllers/3d-game-kit/idle">3D Game Kit/Idle</see></example>
    /// https://kybernetik.com.au/animancer/api/Animancer.Examples.AnimatorControllers.GameKit/IdleState
    ///
    public sealed class ToStopState : CharacterState
    {
        [SerializeField] private bool m_aimToNormal ;
        /************************************************************************************************************************/
        [SerializeField] private TransitionAssetBase _stopLeftBlendTreeAsset;
        [SerializeField] public TransitionAssetBase _stopRightBlendTreeAsset;
        [SerializeField] private float vertical;
        [SerializeField] private float horizontal;

        [SerializeField] public StringAsset _ParameterX;
        [SerializeField] public StringAsset _ParameterY;
        [SerializeField] public SmoothedVector2Parameter _SmoothedParameters;
        [SerializeField, Seconds] public float _ParameterSmoothTime;

        public void Awake()
        {
            _SmoothedParameters = new SmoothedVector2Parameter(
                Character.Animancer,
                _ParameterX,
                _ParameterY,
                _ParameterSmoothTime);
        }

        public override bool CanEnterState => Character.Parameters.IsGrounded;

        private void OnEnable()
        {
            _SmoothedParameters = new SmoothedVector2Parameter(
                Character.Animancer,
                _ParameterX,
                _ParameterY,
                _ParameterSmoothTime);
            
            _stopLeftBlendTreeAsset.Events.OnEnd = OnStopAnimationEnd;
            _stopRightBlendTreeAsset.Events.OnEnd = OnStopAnimationEnd;
        }

        private void OnStopAnimationEnd()
        {
            Character.Parameters.IsStopping = false;
            EventManager.Broadcast(new OnCharacterAnimationFinishedEvent(MovementSubState.Stop,
                Character.Animancer.States.Current.Duration));
        }

        [Header("Stop Animation Tuning")]
        [SerializeField] private float stopAnimationSpeedMultiplier = 2.0f; // Base speed multiplier for stop animations
        [SerializeField] private float highSpeedStopMultiplier = 1.2f; // Additional multiplier for stopping at high speeds
        [SerializeField] private float aimingStopMultiplier = 0.9f; // Slightly slower stops when aiming for more control
        [SerializeField] private float directionChangeInfluence = 0.3f; // How much direction change affects stop animation
        [SerializeField] private float blendDuration = 0.15f; // Blend duration for stop animations

        public override void OnEnterState(float fadeTime, Object conditions = null)
        {
            // Check for special conditions like transitioning from aim to normal
            bool isAimToNormal = false;
            if (conditions != null)
            {
                if(CastTo(conditions, new {AnimName = ""}).AnimName == "AimToNormal")
                {
                    isAimToNormal = true;
                    m_aimToNormal = true;
                }
            }

            // Calculate horizontal and vertical parameters based on movement state
            float rawHorizontal = Character.Parameters.Horizontal.Value;
            float rawVertical = Character.Parameters.Vertical.Value;
            float movementMagnitude = new Vector2(rawHorizontal, rawVertical).magnitude;

            // Process input values based on aiming state
            horizontal = !Character.Parameters.IsAiming ? 0 : rawHorizontal;
            vertical = !Character.Parameters.IsAiming ? Mathf.Abs(rawVertical) : rawVertical;

            // Handle aim-to-normal transition
            if (m_aimToNormal)
            {
                vertical = -vertical;
                m_aimToNormal = false;
            }

            // Calculate stop animation speed based on movement speed and conditions
            float stopSpeed = stopAnimationSpeedMultiplier;

            // Faster stops for higher speeds
            if (movementMagnitude > 0.7f)
            {
                stopSpeed *= Mathf.Lerp(1.0f, highSpeedStopMultiplier, (movementMagnitude - 0.7f) / 0.3f);
            }

            // Slower, more controlled stops when aiming
            if (Character.Parameters.IsAiming && !isAimToNormal)
            {
                stopSpeed *= aimingStopMultiplier;
            }

            // Calculate direction change influence on stop animation
            float directionChange = Mathf.Abs(Character.Parameters.InputAngle.Value);
            if (directionChange > 90f)
            {
                // More dramatic stops for significant direction changes
                float directionFactor = Mathf.Clamp01((directionChange - 90f) / 90f);
                stopSpeed *= (1f + (directionFactor * directionChangeInfluence));
            }

            // Choose appropriate stop animation based on which foot is forward
            AnimancerState state;
            if (!Character.Parameters.IsLeftFootstep)
            {
                state = Character.Animancer.Play(_stopLeftBlendTreeAsset, blendDuration, FadeMode.NormalizedSpeed);
            }
            else
            {
                state = Character.Animancer.Play(_stopRightBlendTreeAsset, blendDuration, FadeMode.NormalizedSpeed);
                
                _SmoothedParameters.TargetValue =
                    new Vector2(horizontal, vertical);
                
            }

            // Apply calculated animation speed
            state.Speed = stopSpeed;

            // Debug info
            if (Debug.isDebugBuild)
            {
                Debug.unityLogger.Log($"Stop animation - Speed: {stopSpeed}, Direction: ({horizontal}, {vertical}), Magnitude: {movementMagnitude}");
            }
        }

        public override void UpdateState()
        {
            //Character.Parameters.UpdateSpeedControl();

            // We use time where Mecanim used normalized time because choosing a number of seconds is much simpler than
            // finding out how long the animation is and working with multiples of that value.
        }

        public override void OnExitState()
        {
            _stopLeftBlendTreeAsset.Events.OnEnd = null;
            _stopRightBlendTreeAsset.Events.OnEnd = null;
        }

        private T CastTo<T>(Object value, T targetType)
        {
            // targetType above is just for compiler magic
            // to infer the type to cast value to
            return (T)value;
        }
}
}