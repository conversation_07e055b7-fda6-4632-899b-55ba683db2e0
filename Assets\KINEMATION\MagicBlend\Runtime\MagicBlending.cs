// Designed by KINEMATION, 2025.

using KINEMATION.KAnimationCore.Runtime.Rig;
using System.Collections.Generic;
using NewAnimancer;
using Unity.Collections;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Experimental.Animations;
using UnityEngine.Playables;

namespace KINEMATION.MagicBlend.Runtime
{
    [HelpURL("https://kinemation.gitbook.io/magic-blend-documentation/")]
    public class MagicBlending : MonoBehaviour
    {
        public Playable OutputPlayable => _playableMixer;
        public PlayableGraph playableGraph;
        public MagicBlendAsset BlendAsset => blendAsset;

        [Tooltip("This asset controls the blending weights.")] [SerializeField]
        private MagicBlendAsset blendAsset;

        public float externalWeight = 1f;

        [Tooltip("Will update weights every frame.")] [SerializeField]
        private bool forceUpdateWeights = true;

        [Tooltip("Will process the Overlay pose. Keep it on most of the time.")] [SerializeField]
        private bool alwaysAnimatePoses = true;

        private const ushort PlayableSortingPriority = 900;

        private Animator _animator;
        private KRigComponent _rigComponent;
        private bool _isAnimancerIntegrationActive = false;

        private AnimationLayerMixerPlayable _playableMixer;
        private NativeArray<BlendStreamAtom> _atoms;

        private PoseJob _poseJob;
        private OverlayJob _overlayJob;
        private LayeringJob _layeringJob;

        private AnimationScriptPlayable _poseJobPlayable;
        private AnimationScriptPlayable _overlayJobPlayable;
        private AnimationScriptPlayable _layeringJobPlayable;

        private bool _isInitialized;
        private float _blendPlayback = 1f;
        private float _blendTime = 0f;
        private AnimationCurve _blendCurve;

        private MagicBlendAsset _desiredBlendAsset;
        private float _desiredBlendTime;
        private bool _useBlendCurve;
        private List<int> _blendedIndexes = new List<int>();

        private Dictionary<string, int> _hierarchyMap;
        private RuntimeAnimatorController _cachedController;
        private AnimationPlayableOutput _magicBlendOutput;

        private bool _forceBlendOut;
        private bool _wasAnimatorActive;
        private bool _isInitializedForAnimancer;

        [Header("Animancer Integration")]
        [Tooltip("If true, Animancer Base Layer output will be fed into MagicBlend as the base pose instead of a static MagicBlendAsset pose.")]
        public bool useAnimancerAsBaseInput = false;

        /// <summary>
        /// Sets a new blending asset.
        /// </summary>
        /// <param name="newAsset">Blending asset.</param>
        /// <param name="useBlending">Whether we need blending.</param>
        /// <param name="blendTime">Blending time in seconds.</param>
        /// <param name="useCurve">Whether we need curve or linear transition.</param>
        public void UpdateMagicBlendAsset(MagicBlendAsset newAsset, bool useBlending = false, float blendTime = -1f,
            bool useCurve = false)
        {
            if (newAsset == null || !_isInitialized)
            {
                Debug.LogWarning("MagicBlending: input asset is NULL!");
                return;
            }

            _desiredBlendAsset = newAsset;
            _useBlendCurve = useCurve;
            _desiredBlendTime = blendTime;

            if (!useBlending)
            {
                _layeringJob.blendWeight = 1f;
                _layeringJobPlayable.SetJobData(_layeringJob);

                SetNewAsset();

                if (!alwaysAnimatePoses)
                {
                    _poseJob.readPose = true;
                    _overlayJob.cachePose = true;

                    _poseJobPlayable.SetJobData(_poseJob);
                    _overlayJobPlayable.SetJobData(_overlayJob);
                }

                return;
            }

            _layeringJob.cachePose = true;
            _layeringJobPlayable.SetJobData(_layeringJob);
        }
        public void StopMagicBlending()
        {
            _forceBlendOut = true;
            _blendPlayback = 0f;
        }

        public void SetOverlayTime(float newTime)
        {
            var overlayPlayable = _overlayJobPlayable.GetInput(0);
            if (!overlayPlayable.IsValid()) return;
            overlayPlayable.SetTime(newTime);
        }

        public float GetOverlayTime(bool isNormalized = true)
        {
            var overlayPlayable = _overlayJobPlayable.GetInput(0);
            if (!overlayPlayable.IsValid() || !blendAsset.isAnimation)
            {
                return 0f;
            }

            float length = (float)overlayPlayable.GetDuration();
            if (Mathf.Approximately(length, 0f))
            {
                return 0f;
            }

            float time = (float)overlayPlayable.GetTime();
            return isNormalized ? Mathf.Clamp01(time / length) : time;
        }

        protected virtual void SetProcessJobs(bool isActive)
        {
            _poseJobPlayable.SetProcessInputs(isActive);
            _overlayJobPlayable.SetProcessInputs(isActive);
        }

        protected virtual void SetNewAsset()
        {
            blendAsset = _desiredBlendAsset;
            _blendCurve = _useBlendCurve ? blendAsset.blendCurve : null;
            _blendTime = _desiredBlendTime > 0f ? _desiredBlendTime : blendAsset.blendTime;

            // Validate bone chains before proceeding
            if (!ValidateBoneChains())
            {
                Debug.LogError("[MagicBlending] SetNewAsset: Bone chain validation failed. Aborting asset change.");
                return;
            }

            // Handle base pose connection based on whether we're using Animancer as base input
            if (useAnimancerAsBaseInput && _isAnimancerIntegrationActive && _animancerBaseLayerPlayable.IsValid())
            {
                // When using Animancer as base input, connect the live animation directly to PoseJob
                // Clear any existing connections first
                if (_poseJobPlayable.GetInputCount() > 0)
                {
                    for (int i = 0; i < _poseJobPlayable.GetInputCount(); i++)
                    {
                        if (_poseJobPlayable.GetInput(i).IsValid())
                        {
                            var existingInput = _poseJobPlayable.GetInput(i);
                            _poseJobPlayable.DisconnectInput(i);
                            // Only destroy if it's not the Animancer base layer
                            if (existingInput.GetHandle() != _animancerBaseLayerPlayable.GetHandle())
                            {
                                existingInput.Destroy();
                            }
                        }
                    }
                }

                // Connect Animancer's live base layer to PoseJob
                _poseJobPlayable.SetInputCount(1);
                _poseJobPlayable.ConnectInput(0, _animancerBaseLayerPlayable, 0);
                _poseJobPlayable.SetInputWeight(0, 1f);

                // CRITICAL: Enable pose reading so PoseJob processes the animated input
                _poseJob.readPose = true;
                _poseJob.alwaysAnimate = true;
                _poseJobPlayable.SetJobData(_poseJob);

                Debug.Log("[MagicBlending] SetNewAsset: Connected Animancer base layer as live base pose and enabled pose reading");
            }
            else
            {
                // Use static base pose from the asset
                MagicBlendLibrary.ConnectPose(_poseJobPlayable, playableGraph, blendAsset.basePose);

                // For static poses, we don't need continuous reading
                _poseJob.readPose = false;
                _poseJob.alwaysAnimate = alwaysAnimatePoses;
                _poseJobPlayable.SetJobData(_poseJob);

                Debug.Log("[MagicBlending] SetNewAsset: Connected static base pose from asset");
            }

            float speed = blendAsset.isAnimation ? blendAsset.overlaySpeed : 0f;
            if (blendAsset.HasOverrides())
            {
                MagicBlendLibrary.ConnectOverlays(_overlayJobPlayable, playableGraph, blendAsset.overlayPose,
                    blendAsset.overrideOverlays, speed);
            }
            else
            {
                MagicBlendLibrary.ConnectPose(_overlayJobPlayable, playableGraph, blendAsset.overlayPose, speed);
            }

            // Reset all weights.
            for (int i = 0; i < _hierarchyMap.Count; i++)
            {
                var atom = _atoms[i];
                atom.baseWeight = atom.additiveWeight = atom.localWeight = 0f;
                _atoms[i] = atom;
            }

            // Add indexes which will be blended.
            _blendedIndexes.Clear();
            foreach (var blend in blendAsset.layeredBlends)
            {
                foreach (var element in blend.layer.elementChain)
                {
                    _hierarchyMap.TryGetValue(element.name, out int index);
                    _blendedIndexes.Add(index);
                }
            }

            // Active the jobs processing.
            SetProcessJobs(true);
            _forceBlendOut = false;

            // Update weights.
            UpdateBlendWeights();
        }

        protected virtual void BuildMagicMixer()
        {
            if (!_playableMixer.IsValid())
            {
                _playableMixer = AnimationLayerMixerPlayable.Create(playableGraph, 3);
                InitializeJobs();
                _playableMixer.ConnectInput(0, _poseJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(1, _overlayJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(2, _layeringJobPlayable, 0, 1f);
            }

            // Check if the output is valid before using it
            if (_magicBlendOutput.IsOutputValid())
            {
                _magicBlendOutput.SetSourcePlayable(_playableMixer);
                _magicBlendOutput.SetSortingOrder(PlayableSortingPriority);
            }
            else
            {
                Debug.LogWarning("[MagicBlending] MagicBlendOutput is not valid, skipping SetSourcePlayable");
            }

            int num = playableGraph.GetOutputCount();
            int animatorPlayableIndex = 0;

            for (int i = 0; i < num; i++)
            {
                var sourcePlayable = playableGraph.GetOutput(i).GetSourcePlayable();
                if (sourcePlayable.GetPlayableType() != typeof(AnimatorControllerPlayable))
                {
                    continue;
                }

                animatorPlayableIndex = i;
            }

            var animatorOutput = playableGraph.GetOutput(animatorPlayableIndex);
            var animatorPlayable = animatorOutput.GetSourcePlayable();

            if (_layeringJobPlayable.IsValid())
            {
                _layeringJobPlayable.DisconnectInput(0);
            }

            _layeringJobPlayable.ConnectInput(0, animatorPlayable, 0, 1f);

            if (blendAsset != null)
            {
                UpdateMagicBlendAsset(blendAsset);
            }
        }

        protected virtual void InitializeMagicBlending()
        {
            playableGraph = _animator.playableGraph;
            _atoms = MagicBlendLibrary.SetupBlendAtoms(_animator, _rigComponent);

            // Only create output if graph is valid
            if (playableGraph.IsValid())
            {
                _magicBlendOutput = AnimationPlayableOutput.Create(playableGraph, "MagicBlendOutput", _animator);
                _isInitialized = true;
                BuildMagicMixer();

                playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);
                playableGraph.Play();
            }
            else
            {
                Debug.LogError("[MagicBlending] Cannot initialize - PlayableGraph is invalid");
            }
        }

        private void InitializeJobs()
        {
            var rootSceneHandle = _animator.BindSceneTransform(_animator.transform);

            _poseJob = new PoseJob()
            {
                atoms = _atoms,
                root = rootSceneHandle,
                alwaysAnimate = alwaysAnimatePoses,
                // If we want poses to be animated every frame, ensure we actually read/apply the pose each frame.
                readPose = alwaysAnimatePoses
            };
            _poseJobPlayable = AnimationScriptPlayable.Create(playableGraph, _poseJob, 1);

            _overlayJob = new OverlayJob()
            {
                atoms = _atoms,
                root = rootSceneHandle,
                alwaysAnimate = alwaysAnimatePoses,
                cachePose = false
            };
            _overlayJobPlayable = AnimationScriptPlayable.Create(playableGraph, _overlayJob, 1);

            _layeringJob = new LayeringJob()
            {
                atoms = _atoms,
                root = rootSceneHandle,
                blendWeight = 1f,
                cachePose = false,
            };
            _layeringJobPlayable = AnimationScriptPlayable.Create(playableGraph, _layeringJob, 1);
        }

        private void OnEnable()
        {
            if (_isInitialized && playableGraph.IsValid())
            {
                BuildMagicMixer();
            }
        }

        protected virtual void Awake()
        {
            if (TryGetComponent<HybridAnimancerComponent>(out _))
            {
                _isAnimancerIntegrationActive = true;
                // Debug.Log("[MagicBlending] Awake: Animancer integration is active.");
            }
            else
            {
                // Debug.Log("[MagicBlending] Awake: Animancer integration is NOT active.");
            }
        }

        public void InitializeForAnimancer(HybridAnimancerComponent animancer)
        {
            if (_isInitialized)
            {
                Debug.LogWarning("[MagicBlending] InitializeForAnimancer called, but already initialized. Skipping.");
                return;
            }

            _animator = GetComponent<Animator>();
            if (_rigComponent == null) _rigComponent = GetComponentInChildren<KRigComponent>();

            if (_animator == null || _rigComponent == null)
            {
                Debug.LogError("[MagicBlending] Animator or KRigComponent not found. Initialization failed for Animancer.");
                return;
            }
            Debug.Log("[MagicBlending] InitializeForAnimancer: Animator and KRigComponent found.");

            _hierarchyMap = new Dictionary<string, int>();
            var hierarchy = _rigComponent.GetRigTransforms();
            for (int i = 0; i < hierarchy.Length; i++)
            {
                if (hierarchy[i] == null) continue;
                _hierarchyMap.Add(hierarchy[i].name, i);
            }

            this.playableGraph = animancer.playableGraph; 
            Debug.Log($"[MagicBlending] InitializeForAnimancer: Using Animancer's PlayableGraph. IsValid: {this.playableGraph.IsValid()}");

            _atoms = MagicBlendLibrary.SetupBlendAtoms(_animator, _rigComponent);
            
            InitializeJobs(); 
            Debug.Log($"[MagicBlending] InitializeForAnimancer: Jobs initialized. _poseJobPlayable.IsValid: {_poseJobPlayable.IsValid()}, _overlayJobPlayable.IsValid: {_overlayJobPlayable.IsValid()}, _layeringJobPlayable.IsValid: {_layeringJobPlayable.IsValid()}");

            if (!_playableMixer.IsValid()) 
            {
                _playableMixer = AnimationLayerMixerPlayable.Create(this.playableGraph, 3);
                _playableMixer.ConnectInput(0, _poseJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(1, _overlayJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(2, _layeringJobPlayable, 0, 1f); 
                Debug.Log($"[MagicBlending] InitializeForAnimancer: PlayableMixer created and inputs connected. IsValid: {_playableMixer.IsValid()}");
            }
            else
            {
                Debug.LogWarning("[MagicBlending] InitializeForAnimancer: _playableMixer was already valid.");
            }

            // Ensure we have an output feeding the Animator so that MagicBlend affects the rig when using Animancer's graph.
            if (!_magicBlendOutput.IsOutputValid())
            {
                _magicBlendOutput = AnimationPlayableOutput.Create(this.playableGraph, "MagicBlendOutput", _animator);
                _magicBlendOutput.SetSortingOrder(PlayableSortingPriority);
                Debug.Log("[MagicBlending] InitializeForAnimancer: Created MagicBlendOutput for Animancer integration.");
            }
            _magicBlendOutput.SetSourcePlayable(_playableMixer);
            
            Debug.Log($"[MagicBlending] InitializeForAnimancer: Attempting to wire graph. Animancer layer count: {animancer.Layers.Count}");
            if (animancer.Layers.Count > 0)
            {
                var baseLayerPlayable = animancer.Layers[0].Playable; 
                Debug.Log($"[MagicBlending] Animancer baseLayerPlayable.IsValid(): {baseLayerPlayable.IsValid()}, _layeringJobPlayable.IsValid(): {_layeringJobPlayable.IsValid()}");

                if (baseLayerPlayable.IsValid() && _layeringJobPlayable.IsValid())
                {
                    // Avoid double-connection errors by ensuring the input is disconnected before reconnecting.
                    if (_layeringJobPlayable.GetInputCount() > 0 && _layeringJobPlayable.GetInput(0).IsValid())
                    {
                        // If it already points to the same playable, skip.
                        var existing = _layeringJobPlayable.GetInput(0);
                        if (existing.Equals(baseLayerPlayable))
                        {
                            Debug.Log("[MagicBlending] LayeringJob already connected to Animancer base layer – skipping reconnect.");
                        }
                        else
                        {
                            Debug.LogWarning("[MagicBlending] LayeringJob had a different input connected – disconnecting before reconnecting to Animancer base layer.");
                            playableGraph.Disconnect(_layeringJobPlayable, 0);
                        }
                    }

                    if (useAnimancerAsBaseInput)
                    {
                        // Feed Animancer base layer into PoseJob, then PoseJob into LayeringJob.
                        if (_poseJobPlayable.GetInputCount() == 0 || !_poseJobPlayable.GetInput(0).IsValid())
                        {
                            playableGraph.Connect(baseLayerPlayable, 0, _poseJobPlayable, 0);
                            _poseJobPlayable.SetInputWeight(0, 1f);
                            Debug.Log("[MagicBlending] Connected Animancer base layer to PoseJob input 0");
                        }

                        // Re-wire LayeringJob to receive PoseJob output instead of base layer.
                        if (_layeringJobPlayable.GetInputCount() > 0 && _layeringJobPlayable.GetInput(0).IsValid())
                        {
                            playableGraph.Disconnect(_layeringJobPlayable, 0);
                        }
                        playableGraph.Connect(_poseJobPlayable, 0, _layeringJobPlayable, 0);
                        _layeringJobPlayable.SetInputWeight(0, 1f);
                        Debug.Log("[MagicBlending] Connected PoseJob output to LayeringJob input 0");
                    }
                    else
                    {
                        // Original behaviour: connect Animancer base layer directly to LayeringJob.
                        if (baseLayerPlayable.GetOutputCount() > 0 && baseLayerPlayable.GetOutput(0).IsValid())
                        {
                            Debug.LogWarning("[MagicBlending] Base layer output 0 already connected (likely to Animancer graph). Skipping connection to LayeringJob to avoid invalid topology.");
                        }
                        else
                        {
                            playableGraph.Connect(baseLayerPlayable, 0, _layeringJobPlayable, 0);
                            _layeringJobPlayable.SetInputWeight(0, 1f);
                        }
                    }
                    Debug.Log("[MagicBlending] Connected Animancer base layer to LayeringJob input. LayeringJob input count after connect: " + _layeringJobPlayable.GetInputCount() + ", IsInputValid(0): " + (_layeringJobPlayable.GetInputCount() > 0 ? _layeringJobPlayable.GetInput(0).IsValid().ToString() : "N/A"));
                }
                else
                {
                    Debug.LogWarning("[MagicBlending] Animancer base layer playable or LayeringJobPlayable is invalid. Cannot connect for LayeringJob input.");
                }
            }
            else
            {
                Debug.LogWarning("[MagicBlending] Animancer has no layers. Cannot connect base layer to LayeringJob input.");
            }
            
            if (blendAsset != null) 
            {
                Debug.Log($"[MagicBlending] InitializeForAnimancer: Processing pre-assigned blendAsset: {blendAsset.name}");
                UpdateMagicBlendAsset(blendAsset);
            }
            else
            {
                Debug.LogWarning("[MagicBlending] InitializeForAnimancer: No blendAsset pre-assigned. The 'input asset is NULL!' warning might appear from UpdateMagicBlendAsset if called later without an asset.");
            }

            this.playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime); 
            _isInitialized = true;
            _isInitializedForAnimancer = true; 
            Debug.Log("[MagicBlending] Initialized for Animancer. _isInitialized=true, _isInitializedForAnimancer=true");
        }

        private void Start()
        {
            if (_isAnimancerIntegrationActive)
            {
                // InitializeForAnimancer should have been called by LayeredAnimationManager.Awake().
                if (!_isInitialized)
                {
                    Debug.LogError("[MagicBlending] Start: Animancer integration is active, but component is not initialized. This indicates InitializeForAnimancer was not successfully called by LayeredAnimationManager or failed. Check LayeredAnimationManager setup, script execution order, and previous logs. Attempting fallback call to InitializeForAnimancer.");
                    if (TryGetComponent<HybridAnimancerComponent>(out var animancerComponent))
                    {
                         InitializeForAnimancer(animancerComponent);
                    }
                    else
                    {
                        Debug.LogError("[MagicBlending] Start: Fallback failed. AnimancerComponent not found for InitializeForAnimancer call.");
                    }
                }
            }
            else // Not Animancer integration
            {
                // OnEnable should have initialized for non-Animancer path.
                // This is a fallback if OnEnable didn't run or complete.
                if (!_isInitialized)
                {
                    Debug.LogWarning("[MagicBlending] Start: Fallback non-Animancer initialization. OnEnable might not have completed its setup.");
                    InitializeMagicBlending();
                    BuildMagicMixer();
                }
            }

            // This logic was for handling blendAsset if not processed during initialization.
            // InitializeForAnimancer now handles its own blendAsset processing.
            // The non-Animancer path (InitializeMagicBlending -> BuildMagicMixer) also processes its asset.
            // The "input asset is NULL!" warning comes from UpdateMagicBlendAsset if it's called with a null asset.
            // If blendAsset is meant to be assigned and used at Start for non-Animancer, ensure it's handled.
            if (blendAsset == null && !_isAnimancerIntegrationActive && _isInitialized)
            {
                // This warning is from the original script, let's keep its spirit for non-Animancer.
                // However, UpdateMagicBlendAsset is called within InitializeMagicBlending if blendAsset is not null.
                // So this specific log might be redundant if blendAsset is consistently null at this point for this path.
                // Debug.LogWarning("[MagicBlending] Start: blendAsset is NULL for non-Animancer setup, and was not processed during InitializeMagicBlending.");
            }
        
            // Try to get Animancer component first.
            if (TryGetComponent<HybridAnimancerComponent>(out var animancerComponentInstance))
            {
                // If Animancer is present, initialize for it.
                InitializeForAnimancer(animancerComponentInstance);
            }
            else
            {
                // Original Start() logic for standard Animator
                _animator = GetComponent<Animator>();
                _cachedController = _animator.runtimeAnimatorController;
                _wasAnimatorActive = _animator.isActiveAndEnabled;

                _rigComponent = GetComponentInChildren<KRigComponent>();
                _hierarchyMap = new Dictionary<string, int>();

                var hierarchy = _rigComponent.GetRigTransforms();
                for (int i = 0; i < hierarchy.Length; i++)
                {
                    _hierarchyMap.Add(hierarchy[i].name, i);
                }

                InitializeMagicBlending();

            if (TryGetComponent<HybridAnimancerComponent>(out var animancerComponent))
            {
                InitializeForAnimancer(animancerComponent);
                // Fallback to original initialization if Animancer is not found
                if (!_isInitialized) // Prevent re-initialization if already done by InitializeForAnimancer
                if (Application.isPlaying)
                {
                    if (_isAnimancerIntegrationActive)
                    {
                        // If Animancer is present, initialization is handled by LayeredAnimationManager
                        // calling InitializeForAnimancer. Do nothing here to avoid conflict.
                        return;
                    }

                    // Standard non-Animancer initialization path
                    if (!_isInitialized)
                    {
                        InitializeMagicBlending(); // Sets up its own playableGraph
                        BuildMagicMixer();         // Uses the graph from InitializeMagicBlending
                    }
                }
            }

            // If standalone and asset exists, ensure it's processed.
            // If Animancer-init, UpdateMagicBlendAsset is called within InitializeForAnimancer if blendAsset is present.
            if (blendAsset != null && !_isInitializedForAnimancer && _isInitialized)
            {
                UpdateMagicBlendAsset(blendAsset);
            }

#if UNITY_EDITOR
            _cachedBlendAsset = blendAsset;
#endif
            }
        }

        protected virtual void UpdateBlendWeights(float globalWeight = 1f)
        {
            int index = 0;

            foreach (var blend in blendAsset.layeredBlends)
            {
                foreach (var unused in blend.layer.elementChain)
                {
                    int realIndex = _blendedIndexes[index];
                    
                    var atom = _atoms[realIndex];
                    atom.baseWeight = blend.baseWeight * blendAsset.globalWeight * globalWeight;
                    atom.additiveWeight = blend.additiveWeight * blendAsset.globalWeight * globalWeight;
                    atom.localWeight = blend.localWeight * blendAsset.globalWeight * globalWeight;
                    _atoms[realIndex] = atom;
                    Debug.Log($"WB {atom.handle}  baseW={blend.baseWeight}  globalW={blendAsset.globalWeight}");
                    
                    index++;
                }
            }

            var overlayPlayable = _overlayJobPlayable.GetInput(0);
            int count = overlayPlayable.GetInputCount();
            if (count > 1)
            {
                for (int i = 1; i < count; i++)
                {
                    overlayPlayable.SetInputWeight(i, blendAsset.overrideOverlays[i - 1].weight);
                }
            }
        }

        protected virtual void Update()
        {
            if (blendAsset == null) return;

            if (!_isInitializedForAnimancer)
            {
                var activeAnimator = _animator.runtimeAnimatorController;
                if (_cachedController != activeAnimator || _wasAnimatorActive != _animator.isActiveAndEnabled)
                {
                    BuildMagicMixer();
                }
                _cachedController = activeAnimator;
                _wasAnimatorActive = _animator.isActiveAndEnabled;
            }

            if (blendAsset.isAnimation)
            {
                var overlayPlayableInput = _overlayJobPlayable.GetInput(0);
                int count = overlayPlayableInput.GetInputCount();

                var overlayPlayable = count == 0 ? overlayPlayableInput : overlayPlayableInput.GetInput(0);

                if (blendAsset.overlayPose.isLooping && overlayPlayable.GetTime() >= blendAsset.overlayPose.length)
                {
                    overlayPlayable.SetTime(0f);
                }

                if (count > 1)
                {
                    for (int i = 1; i < count; i++)
                    {
                        var overrideOverlay = overlayPlayableInput.GetInput(i);
                        var overrideClip = blendAsset.overrideOverlays[i - 1].overlay;

                        if (!overrideClip.isLooping || overrideOverlay.GetTime() < overrideClip.length)
                        {
                            continue;
                        }

                        overrideOverlay.SetTime(0f);
                    }
                }
            }

            float globalWeight = 1f;
            if (_forceBlendOut)
            {
                _blendPlayback = Mathf.Clamp(_blendPlayback + Time.deltaTime, 0f, _blendTime);
                float blendOutWeight = _blendPlayback / _blendTime;
                globalWeight = 1f - (_blendCurve?.Evaluate(blendOutWeight) ?? blendOutWeight);
            }

            if (forceUpdateWeights || _forceBlendOut)
            {
                UpdateBlendWeights(globalWeight * externalWeight);
            }

            if (Mathf.Approximately(globalWeight, 0f))
            {
                SetProcessJobs(false);
                blendAsset = null;
#if UNITY_EDITOR
                _cachedBlendAsset = null;
#endif
            }

            if (_forceBlendOut || Mathf.Approximately(_blendTime, 0f)
                               || Mathf.Approximately(_blendPlayback, _blendTime)) return;

            _blendPlayback = Mathf.Clamp(_blendPlayback + Time.deltaTime, 0f, _blendTime);
            float normalizedWeight = _blendPlayback / _blendTime;

            _layeringJob.blendWeight = _blendCurve?.Evaluate(normalizedWeight) ?? normalizedWeight;
            _layeringJobPlayable.SetJobData(_layeringJob);
        }

        protected virtual void LateUpdate()
        {
            if (!alwaysAnimatePoses && _poseJob.readPose)
            {
                _poseJob.readPose = false;
                _overlayJob.cachePose = false;

                _poseJobPlayable.SetJobData(_poseJob);
                _overlayJobPlayable.SetJobData(_overlayJob);
            }

            if (_layeringJob.cachePose)
            {
                SetNewAsset();

                _blendPlayback = 0f;

                _layeringJob.cachePose = false;
                _layeringJob.blendWeight = 0f;
                _layeringJobPlayable.SetJobData(_layeringJob);

                if (!alwaysAnimatePoses)
                {
                    _poseJob.readPose = true;
                    _overlayJob.cachePose = true;

                    _poseJobPlayable.SetJobData(_poseJob);
                    _overlayJobPlayable.SetJobData(_overlayJob);
                }
            }
        }

        protected virtual void OnDestroy()
        {
            if (playableGraph.IsValid() && playableGraph.IsPlaying())
            {
                playableGraph.Stop();
            }

            if (_atoms.IsCreated)
            {
                _atoms.Dispose();
            }
        }

        public void SetMagicBlendAsset(MagicBlendAsset newAsset)
        {
            blendAsset = newAsset;
        }

        /// <summary>
        /// Validates that all bones in the blend asset chains exist in the rig hierarchy.
        /// This is critical for proper bone index alignment.
        /// </summary>
        private bool ValidateBoneChains()
        {
            if (blendAsset == null || _rigComponent == null)
                return true; // Will validate later when asset is assigned

            var hierarchy = _rigComponent.GetRigTransforms();
            var rigBoneNames = new HashSet<string>();

            for (int i = 0; i < hierarchy.Length; i++)
            {
                if (hierarchy[i] != null)
                    rigBoneNames.Add(hierarchy[i].name);
            }

            bool isValid = true;
            foreach (var blend in blendAsset.layeredBlends)
            {
                foreach (var element in blend.layer.elementChain)
                {
                    if (!rigBoneNames.Contains(element.name))
                    {
                        Debug.LogError($"[MagicBlending] Bone '{element.name}' from blend asset '{blendAsset.name}' not found in rig hierarchy. This will cause bone index misalignment!");
                        isValid = false;
                    }
                }
            }

            if (!isValid)
            {
                Debug.LogError("[MagicBlending] Bone chain validation failed! Make sure all bones (including twist bones) are included in the chains and exist in the rig.");
            }

            return isValid;
        }

        /// <summary>
        /// Initialize MagicBlending for custom PlayableGraph integration (like MxM Motion Matching, Animancer, etc.)
        /// </summary>
        /// <param name="customGraph">The custom PlayableGraph to integrate with</param>
        /// <param name="baseLayerPlayable">The base layer playable that provides the base animation</param>
        public void InitializeForCustomGraph(PlayableGraph customGraph, Playable baseLayerPlayable)
        {
            if (_isInitialized)
            {
                Debug.LogWarning("[MagicBlending] InitializeForCustomGraph called, but already initialized. Skipping.");
                return;
            }

            _animator = GetComponent<Animator>();
            if (_rigComponent == null) _rigComponent = GetComponentInChildren<KRigComponent>();

            if (_animator == null || _rigComponent == null)
            {
                Debug.LogError("[MagicBlending] Animator or KRigComponent not found. Initialization failed for custom graph.");
                return;
            }

            Debug.Log("[MagicBlending] InitializeForCustomGraph: Animator and KRigComponent found.");

            _hierarchyMap = new Dictionary<string, int>();
            var hierarchy = _rigComponent.GetRigTransforms();
            for (int i = 0; i < hierarchy.Length; i++)
            {
                if (hierarchy[i] == null) continue;
                _hierarchyMap.Add(hierarchy[i].name, i);
            }

            playableGraph = customGraph;
            _animancerBaseLayerPlayable = baseLayerPlayable;
            _isAnimancerIntegrationActive = true;

            Debug.Log($"[MagicBlending] InitializeForCustomGraph: Using custom PlayableGraph. IsValid: {playableGraph.IsValid()}");
            Debug.Log($"[MagicBlending] Base layer playable IsValid: {baseLayerPlayable.IsValid()}");

            _atoms = MagicBlendLibrary.SetupBlendAtoms(_animator, _rigComponent);

            InitializeJobs();
            Debug.Log($"[MagicBlending] InitializeForCustomGraph: Jobs initialized.");

            if (!_playableMixer.IsValid())
            {
                _playableMixer = AnimationLayerMixerPlayable.Create(playableGraph, 3);
                _playableMixer.ConnectInput(0, _poseJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(1, _overlayJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(2, _layeringJobPlayable, 0, 1f);
                Debug.Log($"[MagicBlending] InitializeForCustomGraph: PlayableMixer created and inputs connected.");
            }

            // Create output that feeds the Animator
            if (!_magicBlendOutput.IsOutputValid())
            {
                _magicBlendOutput = AnimationPlayableOutput.Create(playableGraph, "MagicBlendOutput", _animator);
                _magicBlendOutput.SetSortingOrder(PlayableSortingPriority);
                Debug.Log("[MagicBlending] InitializeForCustomGraph: Created MagicBlendOutput.");
            }
            _magicBlendOutput.SetSourcePlayable(_playableMixer);

            // Connect the base layer to LayeringJob for final output
            if (baseLayerPlayable.IsValid() && _layeringJobPlayable.IsValid())
            {
                if (_layeringJobPlayable.GetInputCount() > 0)
                {
                    _layeringJobPlayable.DisconnectInput(0);
                }

                _layeringJobPlayable.SetInputCount(1);
                _layeringJobPlayable.ConnectInput(0, baseLayerPlayable, 0);
                _layeringJobPlayable.SetInputWeight(0, 1f);
                Debug.Log("[MagicBlending] Connected base layer to LayeringJob.");
            }

            playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);
            _isInitialized = true;
            _isInitializedForAnimancer = true;
            Debug.Log("[MagicBlending] Initialized for custom graph integration.");
        }

#if UNITY_EDITOR
        private MagicBlendAsset _cachedBlendAsset;

        private void OnValidate()
        {
            if (!_isInitialized)
            {
                return;
            }

            _poseJob.alwaysAnimate = alwaysAnimatePoses;
            _overlayJob.alwaysAnimate = alwaysAnimatePoses;

            _poseJobPlayable.SetJobData(_poseJob);
            _overlayJobPlayable.SetJobData(_overlayJob);

            if (_cachedBlendAsset == blendAsset)
            {
                return;
            }

            UpdateMagicBlendAsset(blendAsset, true, 0f, true);
            (_cachedBlendAsset, blendAsset) = (blendAsset, _cachedBlendAsset);
        }
#endif
    }
}