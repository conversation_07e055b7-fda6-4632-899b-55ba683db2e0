// Designed by KINEMATION, 2025.

using KINEMATION.KAnimationCore.Runtime.Rig;
using System.Collections.Generic;
using NewAnimancer;
using Unity.Collections;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Experimental.Animations;
using UnityEngine.Playables;

namespace KINEMATION.MagicBlend.Runtime
{
    [HelpURL("https://kinemation.gitbook.io/magic-blend-documentation/")]
    public class MagicBlending : MonoBehaviour
    {
        public Playable OutputPlayable => _playableMixer;
        public PlayableGraph playableGraph;
        public MagicBlendAsset BlendAsset => blendAsset;

        [Tooltip("This asset controls the blending weights.")] [SerializeField]
        private MagicBlendAsset blendAsset;

        public float externalWeight = 1f;

        [Tooltip("Will update weights every frame.")] [SerializeField]
        private bool forceUpdateWeights = true;

        [Tooltip("Will process the Overlay pose. Keep it on most of the time.")] [SerializeField]
        private bool alwaysAnimatePoses = true;

        private const ushort PlayableSortingPriority = 900;

        private Animator _animator;
        private KRigComponent _rigComponent;
        private bool _isAnimancerIntegrationActive = false;

        private AnimationLayerMixerPlayable _playableMixer;
        private NativeArray<BlendStreamAtom> _atoms;

        private PoseJob _poseJob;
        private OverlayJob _overlayJob;
        private LayeringJob _layeringJob;

        private AnimationScriptPlayable _poseJobPlayable;
        private AnimationScriptPlayable _overlayJobPlayable;
        private AnimationScriptPlayable _layeringJobPlayable;

        private bool _isInitialized;
        private float _blendPlayback = 1f;
        private float _blendTime = 0f;
        private AnimationCurve _blendCurve;

        private MagicBlendAsset _desiredBlendAsset;
        private float _desiredBlendTime;
        private bool _useBlendCurve;
        private List<int> _blendedIndexes = new List<int>();

        private Dictionary<string, int> _hierarchyMap;
        private RuntimeAnimatorController _cachedController;
        private AnimationPlayableOutput _magicBlendOutput;

        private bool _forceBlendOut;
        private bool _wasAnimatorActive;
        private bool _isInitializedForAnimancer;

        // Stores Animancer's base layer so we can reconnect it after MagicBlend re-configures playables.
        private Playable _animancerBaseLayerPlayable;

        [Header("Animancer Integration")]
        [Tooltip("If true, Animancer Base Layer output will be fed into MagicBlend as the base pose instead of a static MagicBlendAsset pose.")]
        public bool useAnimancerAsBaseInput = false;

        /// <summary>
        /// Sets a new blending asset.
        /// </summary>
        /// <param name="newAsset">Blending asset.</param>
        /// <param name="useBlending">Whether we need blending.</param>
        /// <param name="blendTime">Blending time in seconds.</param>
        /// <param name="useCurve">Whether we need curve or linear transition.</param>
        public void UpdateMagicBlendAsset(MagicBlendAsset newAsset, bool useBlending = false, float blendTime = -1f,
            bool useCurve = false)
        {
            if (newAsset == null || !_isInitialized)
            {
                Debug.LogWarning("MagicBlending: input asset is NULL!");
                return;
            }

            _desiredBlendAsset = newAsset;
            _useBlendCurve = useCurve;
            _desiredBlendTime = blendTime;

            if (!useBlending)
            {
                _layeringJob.blendWeight = 1f;
                _layeringJobPlayable.SetJobData(_layeringJob);

                SetNewAsset();

                if (!alwaysAnimatePoses)
                {
                    _poseJob.readPose = true;
                    _overlayJob.cachePose = true;

                    _poseJobPlayable.SetJobData(_poseJob);
                    _overlayJobPlayable.SetJobData(_overlayJob);
                }

                return;
            }

            _layeringJob.cachePose = true;
            _layeringJobPlayable.SetJobData(_layeringJob);
        }
        public void StopMagicBlending()
        {
            _forceBlendOut = true;
            _blendPlayback = 0f;
        }

        public void SetOverlayTime(float newTime)
        {
            var overlayPlayable = _overlayJobPlayable.GetInput(0);
            if (!overlayPlayable.IsValid()) return;
            overlayPlayable.SetTime(newTime);
        }

        public float GetOverlayTime(bool isNormalized = true)
        {
            var overlayPlayable = _overlayJobPlayable.GetInput(0);
            if (!overlayPlayable.IsValid() || !blendAsset.isAnimation)
            {
                return 0f;
            }

            float length = (float)overlayPlayable.GetDuration();
            if (Mathf.Approximately(length, 0f))
            {
                return 0f;
            }

            float time = (float)overlayPlayable.GetTime();
            return isNormalized ? Mathf.Clamp01(time / length) : time;
        }

        protected virtual void SetProcessJobs(bool isActive)
        {
            _poseJobPlayable.SetProcessInputs(isActive);
            _overlayJobPlayable.SetProcessInputs(isActive);
        }

        protected virtual void SetNewAsset()
        {
            blendAsset = _desiredBlendAsset;
            _blendCurve = _useBlendCurve ? blendAsset.blendCurve : null;
            _blendTime = _desiredBlendTime > 0f ? _desiredBlendTime : blendAsset.blendTime;

            // Handle base pose connection based on whether we're using Animancer as base input
            if (useAnimancerAsBaseInput && _isAnimancerIntegrationActive && _animancerBaseLayerPlayable.IsValid())
            {
                // When using Animancer as base input, connect the live animation directly to PoseJob
                // First, clear any existing connections to avoid conflicts
                if (_poseJobPlayable.GetInputCount() > 0)
                {
                    for (int i = 0; i < _poseJobPlayable.GetInputCount(); i++)
                    {
                        if (_poseJobPlayable.GetInput(i).IsValid())
                        {
                            var existingInput = _poseJobPlayable.GetInput(i);
                            _poseJobPlayable.DisconnectInput(i);
                            // Only destroy if it's not the Animancer base layer (to avoid destroying Animancer's playables)
                            if (existingInput.GetHandle() != _animancerBaseLayerPlayable.GetHandle())
                            {
                                existingInput.Destroy();
                            }
                        }
                    }
                }

                // Ensure we have the right input count and connect Animancer's live base layer
                _poseJobPlayable.SetInputCount(1);
                _poseJobPlayable.ConnectInput(0, _animancerBaseLayerPlayable, 0);
                _poseJobPlayable.SetInputWeight(0, 1f);

                Debug.Log("[MagicBlending] SetNewAsset: Connected Animancer base layer as live base pose");
            }
            else
            {
                // Use static base pose from the asset
                MagicBlendLibrary.ConnectPose(_poseJobPlayable, playableGraph, blendAsset.basePose);
                Debug.Log("[MagicBlending] SetNewAsset: Connected static base pose from asset");
            }

            // Connect overlay pose
            float speed = blendAsset.isAnimation ? blendAsset.overlaySpeed : 0f;
            if (blendAsset.HasOverrides())
            {
                MagicBlendLibrary.ConnectOverlays(_overlayJobPlayable, playableGraph, blendAsset.overlayPose,
                    blendAsset.overrideOverlays, speed);
            }
            else
            {
                MagicBlendLibrary.ConnectPose(_overlayJobPlayable, playableGraph, blendAsset.overlayPose, speed);
            }

            // Reset all weights.
            for (int i = 0; i < _hierarchyMap.Count; i++)
            {
                var atom = _atoms[i];
                atom.baseWeight = atom.additiveWeight = atom.localWeight = 0f;
                _atoms[i] = atom;
            }

            // Add indexes which will be blended.
            _blendedIndexes.Clear();
            foreach (var blend in blendAsset.layeredBlends)
            {
                foreach (var element in blend.layer.elementChain)
                {
                    _hierarchyMap.TryGetValue(element.name, out int index);
                    _blendedIndexes.Add(index);
                }
            }

            // Active the jobs processing.
            SetProcessJobs(true);
            _forceBlendOut = false;

            // Update weights.
            UpdateBlendWeights();
        }

        protected virtual void BuildMagicMixer()
        {
            if (!_playableMixer.IsValid())
            {
                _playableMixer = AnimationLayerMixerPlayable.Create(playableGraph, 3);
                InitializeJobs();
                _playableMixer.ConnectInput(0, _poseJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(1, _overlayJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(2, _layeringJobPlayable, 0, 1f);
            }

            // Check if the output is valid before using it
            if (_magicBlendOutput.IsOutputValid())
            {
                _magicBlendOutput.SetSourcePlayable(_playableMixer);
                _magicBlendOutput.SetSortingOrder(PlayableSortingPriority);
            }
            else
            {
                Debug.LogWarning("[MagicBlending] MagicBlendOutput is not valid, skipping SetSourcePlayable");
            }

            int num = playableGraph.GetOutputCount();
            int animatorPlayableIndex = 0;

            for (int i = 0; i < num; i++)
            {
                var sourcePlayable = playableGraph.GetOutput(i).GetSourcePlayable();
                if (sourcePlayable.GetPlayableType() != typeof(AnimatorControllerPlayable))
                {
                    continue;
                }

                animatorPlayableIndex = i;
            }

            var animatorOutput = playableGraph.GetOutput(animatorPlayableIndex);
            var animatorPlayable = animatorOutput.GetSourcePlayable();

            if (_layeringJobPlayable.IsValid())
            {
                _layeringJobPlayable.DisconnectInput(0);
            }

            _layeringJobPlayable.ConnectInput(0, animatorPlayable, 0, 1f);

            if (blendAsset != null)
            {
                UpdateMagicBlendAsset(blendAsset);
            }
        }

        protected virtual void InitializeMagicBlending()
        {
            playableGraph = _animator.playableGraph;
            _atoms = MagicBlendLibrary.SetupBlendAtoms(_animator, _rigComponent);

            // Only create output if graph is valid
            if (playableGraph.IsValid())
            {
                _magicBlendOutput = AnimationPlayableOutput.Create(playableGraph, "MagicBlendOutput", _animator);
                _isInitialized = true;
                BuildMagicMixer();

                playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);
                playableGraph.Play();
            }
            else
            {
                Debug.LogError("[MagicBlending] Cannot initialize - PlayableGraph is invalid");
            }
        }

        private void InitializeJobs()
        {
            var rootSceneHandle = _animator.BindSceneTransform(_animator.transform);

            _poseJob = new PoseJob()
            {
                atoms = _atoms,
                root = rootSceneHandle,
                alwaysAnimate = alwaysAnimatePoses,
                readPose = false
            };
            _poseJobPlayable = AnimationScriptPlayable.Create(playableGraph, _poseJob, 1);

            _overlayJob = new OverlayJob()
            {
                atoms = _atoms,
                root = rootSceneHandle,
                alwaysAnimate = alwaysAnimatePoses,
                cachePose = false
            };
            _overlayJobPlayable = AnimationScriptPlayable.Create(playableGraph, _overlayJob, 1);

            _layeringJob = new LayeringJob()
            {
                atoms = _atoms,
                root = rootSceneHandle,
                blendWeight = 1f,
                cachePose = false,
            };
            _layeringJobPlayable = AnimationScriptPlayable.Create(playableGraph, _layeringJob, 1);
        }

        private void OnEnable()
        {
            if (_isInitialized && playableGraph.IsValid())
            {
                BuildMagicMixer();
            }
        }

        protected virtual void Awake()
        {
            if (TryGetComponent<HybridAnimancerComponent>(out _))
            {
                _isAnimancerIntegrationActive = true;
                // Debug.Log("[MagicBlending] Awake: Animancer integration is active.");
            }
            else
            {
                // Debug.Log("[MagicBlending] Awake: Animancer integration is NOT active.");
            }
        }

        public void InitializeForAnimancer(HybridAnimancerComponent animancer)
        {
            if (_isInitialized)
            {
                Debug.LogWarning("[MagicBlending] InitializeForAnimancer called, but already initialized. Skipping.");
                return;
            }

            _animator = GetComponent<Animator>();
            if (_rigComponent == null) _rigComponent = GetComponentInChildren<KRigComponent>();

            if (_animator == null || _rigComponent == null)
            {
                Debug.LogError("[MagicBlending] Animator or KRigComponent not found. Initialization failed for Animancer.");
                return;
            }
            Debug.Log("[MagicBlending] InitializeForAnimancer: Animator and KRigComponent found.");

            _hierarchyMap = new Dictionary<string, int>();
            var hierarchy = _rigComponent.GetRigTransforms();
            for (int i = 0; i < hierarchy.Length; i++)
            {
                if (hierarchy[i] == null) continue;
                _hierarchyMap.Add(hierarchy[i].name, i);
            }

            this.playableGraph = animancer.playableGraph; 
            Debug.Log($"[MagicBlending] InitializeForAnimancer: Using Animancer's PlayableGraph. IsValid: {this.playableGraph.IsValid()}");

            _atoms = MagicBlendLibrary.SetupBlendAtoms(_animator, _rigComponent);
            
            InitializeJobs(); 
            Debug.Log($"[MagicBlending] InitializeForAnimancer: Jobs initialized. _poseJobPlayable.IsValid: {_poseJobPlayable.IsValid()}, _overlayJobPlayable.IsValid: {_overlayJobPlayable.IsValid()}, _layeringJobPlayable.IsValid: {_layeringJobPlayable.IsValid()}");

            if (!_playableMixer.IsValid()) 
            {
                _playableMixer = AnimationLayerMixerPlayable.Create(this.playableGraph, 3);
                _playableMixer.ConnectInput(0, _poseJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(1, _overlayJobPlayable, 0, 1f);
                _playableMixer.ConnectInput(2, _layeringJobPlayable, 0, 1f); 
                Debug.Log($"[MagicBlending] InitializeForAnimancer: PlayableMixer created and inputs connected. IsValid: {_playableMixer.IsValid()}");
            }
            else
            {
                Debug.LogWarning("[MagicBlending] InitializeForAnimancer: _playableMixer was already valid.");
            }

            // Ensure we have an output feeding the Animator so that MagicBlend affects the rig when using Animancer's graph.
            if (!_magicBlendOutput.IsOutputValid())
            {
                _magicBlendOutput = AnimationPlayableOutput.Create(this.playableGraph, "MagicBlendOutput", _animator);
                _magicBlendOutput.SetSortingOrder(PlayableSortingPriority);
                Debug.Log("[MagicBlending] InitializeForAnimancer: Created MagicBlendOutput for Animancer integration.");
            }
            _magicBlendOutput.SetSourcePlayable(_playableMixer);
            
            Debug.Log($"[MagicBlending] InitializeForAnimancer: Attempting to wire graph. Animancer layer count: {animancer.Layers.Count}");
            if (animancer.Layers.Count > 0)
            {
                var baseLayerPlayable = animancer.Layers[0].Playable;
                _animancerBaseLayerPlayable = baseLayerPlayable;
                Debug.Log($"[MagicBlending] Animancer baseLayerPlayable.IsValid(): {baseLayerPlayable.IsValid()}, _layeringJobPlayable.IsValid(): {_layeringJobPlayable.IsValid()}");

                if (baseLayerPlayable.IsValid() && _layeringJobPlayable.IsValid())
                {
                    // Always connect Animancer base layer to LayeringJob for final output
                    if (_layeringJobPlayable.GetInputCount() > 0 && _layeringJobPlayable.GetInput(0).IsValid())
                    {
                        Debug.LogWarning("[MagicBlending] LayeringJob already has a valid input. Disconnecting before reconnecting.");
                        playableGraph.Disconnect(_layeringJobPlayable, 0);
                    }

                    playableGraph.Connect(baseLayerPlayable, 0, _layeringJobPlayable, 0);
                    _layeringJobPlayable.SetInputWeight(0, 1f);
                    Debug.Log("[MagicBlending] Connected Animancer base layer to LayeringJob input 0");

                    // Store the base layer playable for use in SetNewAsset
                    // The actual connection to PoseJob will happen in SetNewAsset based on useAnimancerAsBaseInput flag
                    Debug.Log($"[MagicBlending] Stored Animancer base layer playable. useAnimancerAsBaseInput: {useAnimancerAsBaseInput}");

                    Debug.Log("[MagicBlending] Connected Animancer base layer to LayeringJob input. LayeringJob input count after connect: " + _layeringJobPlayable.GetInputCount() + ", IsInputValid(0): " + (_layeringJobPlayable.GetInputCount() > 0 ? _layeringJobPlayable.GetInput(0).IsValid().ToString() : "N/A"));
                }
                else
                {
                    Debug.LogWarning("[MagicBlending] Animancer base layer playable or LayeringJobPlayable is invalid. Cannot connect for LayeringJob input.");
                }
            }
            else
            {
                Debug.LogWarning("[MagicBlending] Animancer has no layers. Cannot connect base layer to LayeringJob input.");
            }
            
            if (blendAsset != null) 
            {
                Debug.Log($"[MagicBlending] InitializeForAnimancer: Processing pre-assigned blendAsset: {blendAsset.name}");
                UpdateMagicBlendAsset(blendAsset);
            }
            else
            {
                Debug.LogWarning("[MagicBlending] InitializeForAnimancer: No blendAsset pre-assigned. The 'input asset is NULL!' warning might appear from UpdateMagicBlendAsset if called later without an asset.");
            }

            this.playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime); 
            _isInitialized = true;
            _isInitializedForAnimancer = true; 
            Debug.Log("[MagicBlending] Initialized for Animancer. _isInitialized=true, _isInitializedForAnimancer=true");
        }

        private void Start()
        {
            if (_isAnimancerIntegrationActive)
            {
                // InitializeForAnimancer should have been called by LayeredAnimationManager.Awake().
                if (!_isInitialized)
                {
                    Debug.LogError("[MagicBlending] Start: Animancer integration is active, but component is not initialized. This indicates InitializeForAnimancer was not successfully called by LayeredAnimationManager or failed. Check LayeredAnimationManager setup, script execution order, and previous logs. Attempting fallback call to InitializeForAnimancer.");
                    if (TryGetComponent<HybridAnimancerComponent>(out var animancerComponent))
                    {
                         InitializeForAnimancer(animancerComponent);
                    }
                    else
                    {
                        Debug.LogError("[MagicBlending] Start: Fallback failed. AnimancerComponent not found for InitializeForAnimancer call.");
                    }
                }
            }
            else // Not Animancer integration
            {
                // Standard non-Animancer initialization
                _animator = GetComponent<Animator>();
                _cachedController = _animator.runtimeAnimatorController;
                _wasAnimatorActive = _animator.isActiveAndEnabled;

                _rigComponent = GetComponentInChildren<KRigComponent>();
                _hierarchyMap = new Dictionary<string, int>();

                var hierarchy = _rigComponent.GetRigTransforms();
                for (int i = 0; i < hierarchy.Length; i++)
                {
                    _hierarchyMap.Add(hierarchy[i].name, i);
                }

                if (!_isInitialized)
                {
                    InitializeMagicBlending();
                }
            }

#if UNITY_EDITOR
            _cachedBlendAsset = blendAsset;
#endif
        }

        protected virtual void UpdateBlendWeights(float globalWeight = 1f)
        {
            int index = 0;

            foreach (var blend in blendAsset.layeredBlends)
            {
                foreach (var unused in blend.layer.elementChain)
                {
                    int realIndex = _blendedIndexes[index];
                    
                    var atom = _atoms[realIndex];
                    atom.baseWeight = blend.baseWeight * blendAsset.globalWeight * globalWeight;
                    atom.additiveWeight = blend.additiveWeight * blendAsset.globalWeight * globalWeight;
                    atom.localWeight = blend.localWeight * blendAsset.globalWeight * globalWeight;
                    _atoms[realIndex] = atom;
                    
                    index++;
                }
            }

            var overlayPlayable = _overlayJobPlayable.GetInput(0);
            int count = overlayPlayable.GetInputCount();
            if (count > 1)
            {
                for (int i = 1; i < count; i++)
                {
                    overlayPlayable.SetInputWeight(i, blendAsset.overrideOverlays[i - 1].weight);
                }
            }
        }

        protected virtual void Update()
        {
            if (blendAsset == null) return;

            if (!_isInitializedForAnimancer)
            {
                var activeAnimator = _animator.runtimeAnimatorController;
                if (_cachedController != activeAnimator || _wasAnimatorActive != _animator.isActiveAndEnabled)
                {
                    BuildMagicMixer();
                }
                _cachedController = activeAnimator;
                _wasAnimatorActive = _animator.isActiveAndEnabled;
            }

            if (blendAsset.isAnimation)
            {
                var overlayPlayableInput = _overlayJobPlayable.GetInput(0);
                int count = overlayPlayableInput.GetInputCount();

                var overlayPlayable = count == 0 ? overlayPlayableInput : overlayPlayableInput.GetInput(0);

                if (blendAsset.overlayPose.isLooping && overlayPlayable.GetTime() >= blendAsset.overlayPose.length)
                {
                    overlayPlayable.SetTime(0f);
                }

                if (count > 1)
                {
                    for (int i = 1; i < count; i++)
                    {
                        var overrideOverlay = overlayPlayableInput.GetInput(i);
                        var overrideClip = blendAsset.overrideOverlays[i - 1].overlay;

                        if (!overrideClip.isLooping || overrideOverlay.GetTime() < overrideClip.length)
                        {
                            continue;
                        }

                        overrideOverlay.SetTime(0f);
                    }
                }
            }

            float globalWeight = 1f;
            if (_forceBlendOut)
            {
                _blendPlayback = Mathf.Clamp(_blendPlayback + Time.deltaTime, 0f, _blendTime);
                float blendOutWeight = _blendPlayback / _blendTime;
                globalWeight = 1f - (_blendCurve?.Evaluate(blendOutWeight) ?? blendOutWeight);
            }

            if (forceUpdateWeights || _forceBlendOut)
            {
                UpdateBlendWeights(globalWeight * externalWeight);
            }

            if (Mathf.Approximately(globalWeight, 0f))
            {
                SetProcessJobs(false);
                blendAsset = null;
#if UNITY_EDITOR
                _cachedBlendAsset = null;
#endif
            }

            if (_forceBlendOut || Mathf.Approximately(_blendTime, 0f)
                               || Mathf.Approximately(_blendPlayback, _blendTime)) return;

            _blendPlayback = Mathf.Clamp(_blendPlayback + Time.deltaTime, 0f, _blendTime);
            float normalizedWeight = _blendPlayback / _blendTime;

            _layeringJob.blendWeight = _blendCurve?.Evaluate(normalizedWeight) ?? normalizedWeight;
            _layeringJobPlayable.SetJobData(_layeringJob);
        }

        protected virtual void LateUpdate()
        {
            if (!alwaysAnimatePoses && _poseJob.readPose)
            {
                _poseJob.readPose = false;
                _overlayJob.cachePose = false;

                _poseJobPlayable.SetJobData(_poseJob);
                _overlayJobPlayable.SetJobData(_overlayJob);
            }

            if (_layeringJob.cachePose)
            {
                SetNewAsset();

                _blendPlayback = 0f;

                _layeringJob.cachePose = false;
                _layeringJob.blendWeight = 0f;
                _layeringJobPlayable.SetJobData(_layeringJob);

                if (!alwaysAnimatePoses)
                {
                    _poseJob.readPose = true;
                    _overlayJob.cachePose = true;

                    _poseJobPlayable.SetJobData(_poseJob);
                    _overlayJobPlayable.SetJobData(_overlayJob);
                }
            }
        }

        protected virtual void OnDestroy()
        {
            if (playableGraph.IsValid() && playableGraph.IsPlaying())
            {
                playableGraph.Stop();
            }

            if (_atoms.IsCreated)
            {
                _atoms.Dispose();
            }
        }

        public void SetMagicBlendAsset(MagicBlendAsset newAsset)
        {
            blendAsset = newAsset;
        }

        /// <summary>
        /// Forces a refresh of the current asset connections.
        /// Call this when useAnimancerAsBaseInput flag changes or when you need to refresh the connections.
        /// </summary>
        public void RefreshAssetConnections()
        {
            if (blendAsset != null && _isInitialized)
            {
                UpdateMagicBlendAsset(blendAsset, false, 0f);
                Debug.Log("[MagicBlending] Refreshed asset connections");
            }
        }

        /// <summary>
        /// Reconnects the Animancer base layer to MagicBlend.
        /// Call this if the Animancer playable graph has been modified.
        /// </summary>
        public void ReconnectAnimancerBaseLayer()
        {
            if (!_isAnimancerIntegrationActive)
                return;

            if (TryGetComponent<HybridAnimancerComponent>(out var animancer) && animancer.Layers.Count > 0)
            {
                var baseLayerPlayable = animancer.Layers[0].Playable;
                if (baseLayerPlayable.IsValid())
                {
                    _animancerBaseLayerPlayable = baseLayerPlayable;

                    // Always reconnect to LayeringJob for final output
                    if (_layeringJobPlayable.IsValid())
                    {
                        if (_layeringJobPlayable.GetInputCount() > 0)
                        {
                            _layeringJobPlayable.DisconnectInput(0);
                        }
                        _layeringJobPlayable.SetInputCount(1);
                        _layeringJobPlayable.ConnectInput(0, baseLayerPlayable, 0);
                        _layeringJobPlayable.SetInputWeight(0, 1f);
                    }

                    // If using Animancer as base input, also reconnect to PoseJob
                    if (useAnimancerAsBaseInput && _poseJobPlayable.IsValid())
                    {
                        // Clear existing connections carefully
                        if (_poseJobPlayable.GetInputCount() > 0)
                        {
                            for (int i = 0; i < _poseJobPlayable.GetInputCount(); i++)
                            {
                                if (_poseJobPlayable.GetInput(i).IsValid())
                                {
                                    var existingInput = _poseJobPlayable.GetInput(i);
                                    _poseJobPlayable.DisconnectInput(i);
                                    // Only destroy if it's not the Animancer base layer
                                    if (existingInput.GetHandle() != baseLayerPlayable.GetHandle())
                                    {
                                        existingInput.Destroy();
                                    }
                                }
                            }
                        }

                        _poseJobPlayable.SetInputCount(1);
                        _poseJobPlayable.ConnectInput(0, baseLayerPlayable, 0);
                        _poseJobPlayable.SetInputWeight(0, 1f);
                        Debug.Log("[MagicBlending] Reconnected Animancer base layer as live base pose");
                    }

                    Debug.Log("[MagicBlending] Reconnected Animancer base layer to MagicBlend");
                }
            }
        }

#if UNITY_EDITOR
        private MagicBlendAsset _cachedBlendAsset;

        private void OnValidate()
        {
            if (!_isInitialized)
            {
                return;
            }

            _poseJob.alwaysAnimate = alwaysAnimatePoses;
            _overlayJob.alwaysAnimate = alwaysAnimatePoses;

            _poseJobPlayable.SetJobData(_poseJob);
            _overlayJobPlayable.SetJobData(_overlayJob);

            if (_cachedBlendAsset == blendAsset)
            {
                return;
            }

            UpdateMagicBlendAsset(blendAsset, true, 0f, true);
            (_cachedBlendAsset, blendAsset) = (blendAsset, _cachedBlendAsset);
        }
#endif
    }
}