// Animancer // https://kybernetik.com.au/animancer // Copyright 2018-2023 Kybernetik //

#pragma warning disable CS0649 // Field is never assigned to, and will always have its default value.

using KINEMATION.MagicBlend.Runtime;
using NewAnimancer;
using NewAnimancer.Units; // Added for MagicBlending integration
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale
{
    public sealed class LayeredAnimationManager : MonoBehaviour
    {
        /************************************************************************************************************************/

        [SerializeField] private HybridAnimancerComponent _Animancer;
        [SerializeField] private AvatarMask _ActionMask;
        [SerializeField, Seconds] float _ActionFadeDuration = AnimancerGraph.DefaultFadeDuration;

        private AnimancerLayer _BaseLayer;
        private AnimancerLayer _ActionLayer;
        private bool _CanPlayActionFullBody;
        private PlayableAssetState _magicBlendState;

        /************************************************************************************************************************/
        // Magic Blend Integration Fields

        [Header("Magic Blend Integration")] [Tooltip("Enable to initialize Magic Blend and allow its use.")]
        public bool initializeMagicBlend = false;

        [Tooltip("Assign your Magic Blend component instance here.")]
        public MagicBlending
            magicBlendComponent;

        [Tooltip("Enable this if bone rebinding logic (SearchAndRebind) should be used as part of Magic Blend setup.")]
        public bool
            enableMagicBlendBoneRebinding =
                false; // This flag indicates intent; actual call to SearchAndRebind is contextual.

        /************************************************************************************************************************/

        /************************************************************************************************************************/

        private void Awake()
        {
            // Auto-resolve references if they weren't set in the Inspector to avoid null-reference issues.
            if (_Animancer == null)
            {
                _Animancer = GetComponent<HybridAnimancerComponent>();
                if (_Animancer == null)
                {
                    Debug.LogError("[LayeredAnimationManager] Awake: HybridAnimancerComponent reference is missing and could not be found on the same GameObject.");
                    enabled = false;
                    return;
                }
            }

            if (magicBlendComponent == null && initializeMagicBlend)
            {
                magicBlendComponent = GetComponentInChildren<MagicBlending>();
                if (magicBlendComponent == null)
                {
                    Debug.LogError("[LayeredAnimationManager] Awake: MagicBlending component reference is missing and could not be auto-located.");
                }
            }

            if (_Animancer.Layers.Count < 2)
            {
                Debug.LogError("[LayeredAnimationManager] Awake: Expected at least 2 Animancer layers (Base + Action). Configure the Animancer component accordingly.");
            }

            _BaseLayer = _Animancer.Layers[0];
            if (_Animancer.Layers.Count > 1)
            {
                _ActionLayer = _Animancer.Layers[1];
            }
            else
            {
                _ActionLayer = _Animancer.Layers[0]; // Fallback to base layer if action layer missing
            }

            _ActionLayer.Mask = _ActionMask;

            // Magic Blend PlayableGraph Initialization
            if (initializeMagicBlend && magicBlendComponent != null && _Animancer != null)
            {
                // Call the custom initializer we added to MagicBlending.cs
                magicBlendComponent.InitializeForAnimancer(_Animancer);
                //Debug.Log("[LayeredAnimationManager] Magic Blend: Initialized for Animancer.");
            }
        }

        /************************************************************************************************************************/

        public void PlayBase(ITransition transition, bool canPlayActionFullBody)
        {
            _CanPlayActionFullBody = canPlayActionFullBody;

            if (_CanPlayActionFullBody && _ActionLayer.TargetWeight > 0)
            {
                PlayActionFullBody(_ActionFadeDuration);
            }
            else
            {
                _BaseLayer.Play(transition);
            }
        }

        /************************************************************************************************************************/

        public void PlayAction(ITransition transition)
        {
            _ActionLayer.Play(transition);

            if (_CanPlayActionFullBody)
                PlayActionFullBody(transition.FadeDuration);
        }

        /************************************************************************************************************************/

        private void PlayActionFullBody(float fadeDuration)
        {
            var actionState = _ActionLayer.CurrentState;
            var baseState = _BaseLayer.Play(actionState.Clip, fadeDuration);
            baseState.NormalizedTime = actionState.NormalizedTime;
        }

        /************************************************************************************************************************/

        public void TriggerMagicBlend(MagicBlendAsset asset, bool useBlending = false, float blendTime = -1f)
        {
            if (!initializeMagicBlend || magicBlendComponent == null)
            {
                Debug.LogWarning("[LayeredAnimationManager] Magic Blend not initialized or component not assigned. Cannot apply blend.");
                return;
            }

            if (asset == null)
            {
                Debug.LogError("[LayeredAnimationManager] Invalid MagicBlendAsset provided.");
                return;
            }

            magicBlendComponent.UpdateMagicBlendAsset(asset, useBlending, blendTime);

            if (_magicBlendState == null)
            {
                _magicBlendState = new PlayableAssetState(); // state will be added to layer automatically when played
            }

            _magicBlendState.InputPlayable = magicBlendComponent.OutputPlayable;

            float fadeDuration = blendTime >= 0f ? blendTime : _ActionFadeDuration;

            // FIXED: Play MagicBlend on Base Layer instead of Action Layer
            // This ensures the base pose is visible and properly blended
            Debug.Log($"[LayeredAnimationManager] Playing MagicBlend asset '{asset.name}' on BASE LAYER with fade duration {fadeDuration}s");
            var playedState = _BaseLayer.Play(_magicBlendState, fadeDuration);

            Debug.Log($"[LayeredAnimationManager] MagicBlend played on Base Layer. State: {playedState}, Base Pose: {(asset.basePose ? asset.basePose.name : "null")}, Overlay: {(asset.overlayPose ? asset.overlayPose.name : "null")}");
        }

        /************************************************************************************************************************/

        /// <summary>
        /// Triggers MagicBlend on a specific layer (0 = Base Layer, 1 = Action Layer)
        /// </summary>
        /// <param name="asset">The MagicBlendAsset to play</param>
        /// <param name="layerIndex">Layer index (0 for base, 1 for action)</param>
        /// <param name="useBlending">Whether to use MagicBlend's internal blending</param>
        /// <param name="blendTime">Blend time in seconds</param>
        public void TriggerMagicBlendOnLayer(MagicBlendAsset asset, int layerIndex = 0, bool useBlending = false, float blendTime = -1f)
        {
            if (!initializeMagicBlend || magicBlendComponent == null)
            {
                Debug.LogWarning("[LayeredAnimationManager] Magic Blend not initialized or component not assigned. Cannot apply blend.");
                return;
            }

            if (asset == null)
            {
                Debug.LogError("[LayeredAnimationManager] Invalid MagicBlendAsset provided.");
                return;
            }

            if (layerIndex < 0 || layerIndex >= _Animancer.Layers.Count)
            {
                Debug.LogError($"[LayeredAnimationManager] Invalid layer index: {layerIndex}. Available layers: {_Animancer.Layers.Count}");
                return;
            }

            magicBlendComponent.UpdateMagicBlendAsset(asset, useBlending, blendTime);

            if (_magicBlendState == null)
            {
                _magicBlendState = new PlayableAssetState();
            }

            _magicBlendState.InputPlayable = magicBlendComponent.OutputPlayable;

            float fadeDuration = blendTime >= 0f ? blendTime : _ActionFadeDuration;
            var targetLayer = _Animancer.Layers[layerIndex];
            string layerName = layerIndex == 0 ? "BASE" : "ACTION";

            Debug.Log($"[LayeredAnimationManager] Playing MagicBlend asset '{asset.name}' on {layerName} LAYER (index {layerIndex}) with fade duration {fadeDuration}s");
            var playedState = targetLayer.Play(_magicBlendState, fadeDuration);

            Debug.Log($"[LayeredAnimationManager] MagicBlend played on {layerName} Layer. State: {playedState}, Base Pose: {(asset.basePose ? asset.basePose.name : "null")}, Overlay: {(asset.overlayPose ? asset.overlayPose.name : "null")}");
        }

        /************************************************************************************************************************/

        public void FadeOutUpperBody()
        {
            _ActionLayer.StartFade(0, _ActionFadeDuration);
        }

        /// <summary>
        /// Searches for a bone in the original rig and reparents the target bone to it, resetting its local transform and renaming it.
        /// </summary>
        /// <param name="boneTransform">The bone to be reparented (e.g., from a clothing item).</param>
        /// <param name="originalRigRoot">The root of the character's original skeleton.</param>
        public static void SearchAndRebind(Transform boneTransform, Transform originalRigRoot)
        {
            if (boneTransform == null || originalRigRoot == null)
            {
                Debug.LogError("[LayeredAnimationManager] SearchAndRebind: boneTransform or originalRigRoot is null.");
                return;
            }

            // Attempt to find the identically named bone in the original rig.
            Transform matchingTransform = FindRecursive(originalRigRoot, boneTransform.name);

            if (matchingTransform != null)
            {
                boneTransform.SetParent(matchingTransform);
                boneTransform.localPosition = Vector3.zero;
                boneTransform.localRotation = Quaternion.identity;

                // Add suffix if not already present to avoid duplication like "NameClothingClothing"
                if (!boneTransform.name.EndsWith("Clothing"))
                {
                    boneTransform.name += "Clothing";
                }
            }
            else
            {
                //Debug.LogWarning($"[LayeredAnimationManager.SearchAndRebind] No matching bone found in original rig for: {boneTransform.name}");
            }
        }

        /// <summary>
        /// Helper to find a transform by name recursively.
        /// </summary>
        private static Transform FindRecursive(Transform parent, string name)
        {
            if (parent.name == name) return parent;
            foreach (Transform child in parent)
            {
                Transform found = FindRecursive(child, name);
                if (found != null) return found;
            }
            return null;
        }

        /************************************************************************************************************************/
    }
}