using KINEMATION.MagicBlend.Runtime;
using UnityEngine;
using UnityEngine.Playables;

/// <summary>
/// Example integration script for custom animation systems like MxM Motion Matching.
/// This shows how to properly integrate MagicBlend with custom PlayableGraphs.
/// </summary>
public class CustomAnimationSystemIntegration : MonoBehaviour
{
    [Header("Required Components")]
    [SerializeField] private MagicBlending magicBlending;
    
    [Header("Custom Animation System")]
    [SerializeField] private bool useMxMMotionMatching = false;
    [SerializeField] private bool useCustomPlayableGraph = false;
    
    [Header("MagicBlend Assets")]
    [SerializeField] private MagicBlendAsset aimingBlend;
    [SerializeField] private MagicBlendAsset reloadBlend;
    
    // Custom animation system references (replace with your actual system)
    private PlayableGraph customGraph;
    private Playable customBaseLayerPlayable;
    private bool isInitialized = false;

    private void Start()
    {
        if (magicBlending == null)
            magicBlending = GetComponent<MagicBlending>();
        
        InitializeCustomAnimationSystem();
    }

    private void InitializeCustomAnimationSystem()
    {
        if (useMxMMotionMatching)
        {
            InitializeMxMIntegration();
        }
        else if (useCustomPlayableGraph)
        {
            InitializeCustomGraphIntegration();
        }
        else
        {
            Debug.LogWarning("[CustomAnimationSystemIntegration] No custom animation system selected.");
        }
    }

    /// <summary>
    /// Example integration with MxM Motion Matching system.
    /// Replace this with your actual MxM integration code.
    /// </summary>
    private void InitializeMxMIntegration()
    {
        // Example: Get MxM's PlayableGraph
        // var mxmAnimator = GetComponent<MxMAnimator>(); // Replace with actual MxM component
        // customGraph = mxmAnimator.PlayableGraph; // Replace with actual MxM graph access
        // customBaseLayerPlayable = mxmAnimator.GetBaseLayerPlayable(); // Replace with actual method
        
        // For demonstration purposes, create a simple graph
        customGraph = PlayableGraph.Create("CustomMxMGraph");
        var animator = GetComponent<Animator>();
        var output = AnimationPlayableOutput.Create(customGraph, "MxMOutput", animator);
        
        // Create a simple animation playable (replace with MxM's actual playable)
        var clip = Resources.Load<AnimationClip>("DefaultWalk"); // Replace with your clip
        if (clip != null)
        {
            customBaseLayerPlayable = AnimationClipPlayable.Create(customGraph, clip);
            output.SetSourcePlayable(customBaseLayerPlayable);
        }
        else
        {
            Debug.LogWarning("[CustomAnimationSystemIntegration] No default animation clip found for MxM demo.");
            return;
        }
        
        customGraph.Play();
        
        // Initialize MagicBlend with the custom graph
        if (magicBlending != null && customBaseLayerPlayable.IsValid())
        {
            magicBlending.InitializeForCustomGraph(customGraph, customBaseLayerPlayable);
            isInitialized = true;
            Debug.Log("[CustomAnimationSystemIntegration] MxM integration initialized successfully.");
        }
    }

    /// <summary>
    /// Example integration with a custom PlayableGraph system.
    /// </summary>
    private void InitializeCustomGraphIntegration()
    {
        // Create a custom PlayableGraph
        customGraph = PlayableGraph.Create("CustomAnimationGraph");
        var animator = GetComponent<Animator>();
        var output = AnimationPlayableOutput.Create(customGraph, "CustomOutput", animator);
        
        // Create your custom animation playables here
        // This is just an example - replace with your actual animation system
        var clip = Resources.Load<AnimationClip>("DefaultIdle");
        if (clip != null)
        {
            customBaseLayerPlayable = AnimationClipPlayable.Create(customGraph, clip);
            output.SetSourcePlayable(customBaseLayerPlayable);
        }
        else
        {
            Debug.LogWarning("[CustomAnimationSystemIntegration] No default animation clip found for custom graph demo.");
            return;
        }
        
        customGraph.Play();
        
        // Initialize MagicBlend with the custom graph
        if (magicBlending != null && customBaseLayerPlayable.IsValid())
        {
            magicBlending.InitializeForCustomGraph(customGraph, customBaseLayerPlayable);
            isInitialized = true;
            Debug.Log("[CustomAnimationSystemIntegration] Custom graph integration initialized successfully.");
        }
    }

    private void Update()
    {
        if (!isInitialized) return;
        
        // Example input handling for MagicBlend overlays
        if (Input.GetKeyDown(KeyCode.Mouse1)) // Right click for aiming
        {
            PlayMagicBlendOverlay(aimingBlend, "Aiming");
        }
        
        if (Input.GetKeyDown(KeyCode.R)) // R for reload
        {
            PlayMagicBlendOverlay(reloadBlend, "Reloading");
        }
    }

    /// <summary>
    /// Plays a MagicBlend overlay on top of the custom animation system.
    /// </summary>
    private void PlayMagicBlendOverlay(MagicBlendAsset asset, string actionName)
    {
        if (asset == null || magicBlending == null)
        {
            Debug.LogWarning($"[CustomAnimationSystemIntegration] Cannot play {actionName}: missing asset or MagicBlending component.");
            return;
        }

        try
        {
            // Enable animated base input so MagicBlend uses the live animation from our custom system
            magicBlending.useAnimancerAsBaseInput = true;
            
            // Update the asset
            magicBlending.UpdateMagicBlendAsset(asset);
            
            Debug.Log($"[CustomAnimationSystemIntegration] Playing MagicBlend overlay: {actionName}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[CustomAnimationSystemIntegration] Failed to play MagicBlend overlay {actionName}: {e.Message}");
        }
    }

    /// <summary>
    /// Updates the base layer playable reference.
    /// Call this if your custom animation system changes its base playable.
    /// </summary>
    public void UpdateBaseLayerPlayable(Playable newBaseLayerPlayable)
    {
        if (!isInitialized || !newBaseLayerPlayable.IsValid()) return;
        
        customBaseLayerPlayable = newBaseLayerPlayable;
        
        // Reinitialize MagicBlend with the new base layer
        if (magicBlending != null)
        {
            magicBlending.InitializeForCustomGraph(customGraph, customBaseLayerPlayable);
            Debug.Log("[CustomAnimationSystemIntegration] Updated base layer playable and reinitialized MagicBlend.");
        }
    }

    private void OnDestroy()
    {
        if (customGraph.IsValid())
        {
            customGraph.Destroy();
        }
    }

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 150));
        GUILayout.Label("Custom Animation System Integration", GUI.skin.box);
        
        GUILayout.Label($"Initialized: {isInitialized}");
        GUILayout.Label($"Custom Graph Valid: {(customGraph.IsValid() ? "Yes" : "No")}");
        GUILayout.Label($"Base Playable Valid: {(customBaseLayerPlayable.IsValid() ? "Yes" : "No")}");
        
        GUILayout.Space(10);
        GUILayout.Label("Controls:");
        GUILayout.Label("Right Click - Aim, R - Reload");
        
        if (GUILayout.Button("Refresh MagicBlend Connections"))
        {
            if (magicBlending != null)
            {
                magicBlending.RefreshAssetConnections();
            }
        }
        
        GUILayout.EndArea();
    }
}
