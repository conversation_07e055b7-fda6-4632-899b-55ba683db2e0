using Events;
using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;

namespace PlayerFAP.Components.Detection
{
    // Helper class to find the singleton entity from MonoBehaviour
    public class DetectionTargetSingleton : MonoBehaviour
    {
        private World defaultWorld;
        private EntityManager entityManager;
        private EntityQuery singletonQuery;
        private float lastUpdateTime;
        private const float UPDATE_INTERVAL = 0.1f; // 100ms update interval for stability

        private void Awake()
        {
            defaultWorld = World.DefaultGameObjectInjectionWorld;
            entityManager = defaultWorld.EntityManager;
            
            EventManager.Subscribe<OnLostTargetEvent>(OnLostTarget);
            // Query for our singleton
            singletonQuery = entityManager.CreateEntityQuery(
                ComponentType.ReadOnly<DetectionTargetComponent>(),
                ComponentType.ReadOnly<DetectedEnemyBuffer>()
            );
        }

        private void OnLostTarget(OnLostTargetEvent lostTargetEvent)
        {
            var targetComponent = entityManager.GetComponentData<DetectionTargetComponent>(singletonQuery.GetSingletonEntity());
            targetComponent.IsAiming = false;
            targetComponent.HasTarget = false;
            targetComponent.IsDetected = false;
            targetComponent.IsInFOV = false;
            targetComponent.CurrentPosition = float3.zero;
            targetComponent.Score = 0;
            entityManager.SetComponentData(singletonQuery.GetSingletonEntity(), targetComponent);
        }

        public (Entity currentTarget, Vector3 targetPosition, bool hasTarget, bool isInFOV, float lastKnownDistance, float score) GetCurrentTarget()
        {
            if (singletonQuery.IsEmpty) 
                return (Entity.Null, Vector3.zero, false, false, 0f,0f);

            var targetComponent = entityManager.GetComponentData<DetectionTargetComponent>(singletonQuery.GetSingletonEntity());
            // var currentTime = Time.time;
            //
            // // Only update if enough time has passed
            // if (currentTime - lastUpdateTime >= UPDATE_INTERVAL)
            // {
            //     lastUpdateTime = currentTime;
            // }

            return (targetComponent.CurrentTarget, 
                   new Vector3(targetComponent.CurrentPosition.x, targetComponent.CurrentPosition.y, targetComponent.CurrentPosition.z),
                   targetComponent.HasTarget,
                   targetComponent.IsInFOV,
                   math.length(targetComponent.CurrentPosition - targetComponent.LastKnownPosition),targetComponent.Score);
        }

        public DetectedEnemy[] GetDetectedEnemies(float3 playerPosition)
        {
            if (singletonQuery.IsEmpty) 
                return new DetectedEnemy[0];

            var buffer = entityManager.GetBuffer<DetectedEnemyBuffer>(singletonQuery.GetSingletonEntity());
            var enemies = new DetectedEnemy[buffer.Length];
            
            for (int i = 0; i < buffer.Length; i++)
            {
                var enemy = buffer[i];
                enemies[i] = new DetectedEnemy
                {
                    Entity = enemy.Entity,
                    Position = new Vector3(enemy.Position.x, enemy.Position.y, enemy.Position.z),
                    Distance = math.length(enemy.Position - playerPosition), // Keep this for compatibility
                    IsInFOV = enemy.IsInFOV,
                    Score = enemy.Score

                };
            }

            return enemies;
        }

        public int GetDetectedEnemyCount()
        {
            if (singletonQuery.IsEmpty) 
                return 0;

            var buffer = entityManager.GetBuffer<DetectedEnemyBuffer>(singletonQuery.GetSingletonEntity());
            return buffer.Length;
        }
    }

    public struct DetectedEnemy
    {
        public Entity Entity;
        public Vector3 Position;
        public float Distance;
        public bool IsInFOV;
        public float Score;
    }
}
