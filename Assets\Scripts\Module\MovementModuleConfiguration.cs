using UnityEngine;
using Sirenix.OdinInspector;

namespace Module.Mono.Animancer.RealsticFemale
{
    /// <summary>
    /// Configuration for the movement system, following the Scriptable Object pattern
    /// as specified in rules.md
    /// </summary>
    [CreateAssetMenu(fileName = "MovementConfiguration", menuName = "Configuration/MovementConfiguration")]
    public class MovementModuleConfiguration : ScriptableObject
    {
        [Header("Movement Improvement")]
        [Tooltip("Enable improved movement for smoother direction changes")]
        public bool improvedMovement = false;
        
        [Header("Movement Parameters")]
        [Tooltip("Time to reach full speed")]
        public float accelerationTime = 0.1f;
        
        [Tooltip("Time to stop from full speed")]
        public float decelerationTime = 0.2f;
        
        [Tooltip("Slower movement while aiming")]
        [Range(0.1f, 1f)]
        public float aimingSpeedMultiplier = 0.8f;
        
        [Tooltip("Slightly slower when strafing sideways")]
        [Range(0.1f, 1f)]
        public float strafingSpeedMultiplier = 0.9f;
        
        [Tooltip("Slower when moving backward")]
        [Range(0.1f, 1f)]
        public float backpedalSpeedMultiplier = 0.7f;
        
        [Tooltip("Smooth direction changes")]
        public AnimationCurve directionChangeCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

        [Header("Speed and Rotation")]
        [Tooltip("Base movement speed")]
        public float moveSpeed = 5.0f;
        
        [Tooltip("Rotation speed in degrees per second")]
        public float rotationSpeed = 720.0f;

        [Header("Target Rotation Parameters")]
        [Tooltip("Minimum angle difference to trigger whole body rotation")]
        public float minRotationAngle = 30f;
        
        [Tooltip("Toggle for whole body rotation")]
        public bool useWholeBodyRotation = true;
        
        [Tooltip("Smoothing time for rotation")]
        public float rotationSmoothTime = 0.2f;
        
        [Header("Transition Parameters")]
        [Tooltip("Duration for fast rotation tween")]
        public float fastRotationSpeed = 0.2f;
        
        [Tooltip("Wait time for stop state")]
        public float waitForStop = 0.5f;
        
        [Tooltip("Cooldown between state transitions")]
        public float transitionCooldown = 0.25f;
        
    }
}
