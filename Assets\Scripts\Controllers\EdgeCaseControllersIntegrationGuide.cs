// using UnityEngine;
// using Controllers;
//
// namespace Controllers
// {
//     /// <summary>
//     /// Integration guide and helper for the new edge case controllers
//     /// This script provides examples and utilities for integrating the new systems
//     /// </summary>
//     public class EdgeCaseControllersIntegrationGuide : MonoBehaviour
//     {
//         [Header("Controller References")]
//         [SerializeField] private UnifiedSpeedController speedController;
//         [SerializeField] private EnhancedInputProcessor inputProcessor;
//         [SerializeField] private AdvancedUpperBodyRotationController rotationController;
//         
//         [Header("Integration Settings")]
//         [SerializeField] private bool autoSetupControllers = true;
//         [SerializeField] private bool enableDebugMode = false;
//         [SerializeField] private bool showPerformanceStats = false;
//         
//         [Header("Performance Monitoring")]
//         [SerializeField] private float performanceUpdateInterval = 1f;
//         private float lastPerformanceUpdate;
//         
//         #region Unity Lifecycle
//         
//         private void Awake()
//         {
//             if (autoSetupControllers)
//             {
//                 SetupControllers();
//             }
//         }
//         
//         private void Start()
//         {
//             InitializeControllers();
//         }
//         
//         private void Update()
//         {
//             if (showPerformanceStats && Time.time - lastPerformanceUpdate >= performanceUpdateInterval)
//             {
//                 UpdatePerformanceStats();
//                 lastPerformanceUpdate = Time.time;
//             }
//         }
//         
//         #endregion
//         
//         #region Controller Setup
//         
//         /// <summary>
//         /// Automatically setup controllers if not assigned
//         /// </summary>
//         private void SetupControllers()
//         {
//             // Get or add UnifiedSpeedController
//             if (speedController == null)
//             {
//                 speedController = GetComponent<UnifiedSpeedController>();
//                 if (speedController == null)
//                 {
//                     speedController = gameObject.AddComponent<UnifiedSpeedController>();
//                     Debug.Log("[Integration] Added UnifiedSpeedController");
//                 }
//             }
//             
//             // Get or add EnhancedInputProcessor
//             if (inputProcessor == null)
//             {
//                 inputProcessor = GetComponent<EnhancedInputProcessor>();
//                 if (inputProcessor == null)
//                 {
//                     inputProcessor = gameObject.AddComponent<EnhancedInputProcessor>();
//                     Debug.Log("[Integration] Added EnhancedInputProcessor");
//                 }
//             }
//             
//             // Get or add AdvancedUpperBodyRotationController
//             if (rotationController == null)
//             {
//                 rotationController = GetComponent<AdvancedUpperBodyRotationController>();
//                 if (rotationController == null)
//                 {
//                     rotationController = gameObject.AddComponent<AdvancedUpperBodyRotationController>();
//                     Debug.Log("[Integration] Added AdvancedUpperBodyRotationController");
//                 }
//             }
//         }
//         
//         /// <summary>
//         /// Initialize all controllers with optimal settings
//         /// </summary>
//         private void InitializeControllers()
//         {
//             if (speedController != null)
//             {
//                 // Configure speed controller for your game
//                 speedController.SetBaseMovementSpeed(5f); // Adjust as needed
//                 Debug.Log("[Integration] UnifiedSpeedController initialized");
//             }
//             
//             if (inputProcessor != null)
//             {
//                 // Configure input processor
//                 inputProcessor.SetDeadzone(0.1f); // Adjust as needed
//                 inputProcessor.SetSensitivity(1.2f); // Adjust as needed
//                 Debug.Log("[Integration] EnhancedInputProcessor initialized");
//             }
//             
//             if (rotationController != null)
//             {
//                 // Rotation controller initializes automatically
//                 Debug.Log("[Integration] AdvancedUpperBodyRotationController initialized");
//             }
//         }
//         
//         #endregion
//         
//         #region Integration Examples
//         
//         /// <summary>
//         /// Example: How to integrate with existing MovementModule
//         /// Call this from your MovementModule.ApplyMovement method
//         /// </summary>
//         public void IntegrateWithMovementModule(Vector2 inputVector, bool isAiming, bool isSprinting)
//         {
//             if (inputProcessor != null)
//             {
//                 // Set raw input for processing
//                 inputProcessor.SetRawInput(inputVector);
//                 
//                 // Get processed input (with angle wrapping protection and smoothing)
//                 Vector2 processedInput = inputProcessor.GetProcessedInput();
//                 float inputAngle = inputProcessor.GetInputAngle();
//                 
//                 // Use processed input instead of raw input in your movement calculations
//                 // Replace: Vector3 inputDirection = new Vector3(inputVector.x, 0, inputVector.y);
//                 // With:    Vector3 inputDirection = new Vector3(processedInput.x, 0, processedInput.y);
//             }
//             
//             if (speedController != null)
//             {
//                 // Update movement context
//                 speedController.UpdateMovementInput(inputVector);
//                 speedController.SetSprinting(isSprinting);
//                 
//                 // Get calculated speed (fixes close enemy speed issues)
//                 float calculatedSpeed = speedController.GetCurrentSpeed();
//                 
//                 // Use calculated speed instead of fixed speed in your movement
//                 // Replace: float moveSpeed = 5.0f;
//                 // With:    float moveSpeed = calculatedSpeed;
//             }
//             
//             if (rotationController != null)
//             {
//                 // Update rotation context
//                 float movementSpeed = speedController?.GetCurrentSpeed() ?? 0f;
//                 rotationController.SetMovementSpeed(movementSpeed);
//                 
//                 // The rotation controller handles upper body rotation automatically
//                 // through event subscriptions, no direct calls needed
//             }
//         }
//         
//         /// <summary>
//         /// Example: How to integrate with existing InputModule
//         /// Call this from your InputModule.UpdateModule method
//         /// </summary>
//         public void IntegrateWithInputModule(float rawHorizontal, float rawVertical)
//         {
//             if (inputProcessor != null)
//             {
//                 Vector2 rawInput = new Vector2(rawHorizontal, rawVertical);
//                 inputProcessor.SetRawInput(rawInput);
//                 
//                 // Get enhanced input values
//                 Vector2 processedInput = inputProcessor.GetProcessedInput();
//                 float inputAngle = inputProcessor.GetInputAngle();
//                 bool isDirectionChanging = inputProcessor.IsDirectionChanging();
//                 
//                 // Use these values in your input processing instead of raw values
//                 // This prevents virtual joystick glitches and provides smooth input
//             }
//         }
//         
//         /// <summary>
//         /// Example: How to integrate with existing AimingModule
//         /// Call this when target position changes
//         /// </summary>
//         public void IntegrateWithAimingModule(Vector3 targetPosition)
//         {
//             if (rotationController != null)
//             {
//                 rotationController.SetTargetPosition(targetPosition);
//                 // The controller will automatically calculate optimal rotation limits
//             }
//             
//             if (speedController != null)
//             {
//                 // Speed controller automatically receives target events
//                 // No direct calls needed due to event-driven architecture
//             }
//         }
//         
//         #endregion
//         
//         #region Performance Monitoring
//         
//         /// <summary>
//         /// Update and display performance statistics
//         /// </summary>
//         private void UpdatePerformanceStats()
//         {
//             if (!showPerformanceStats) return;
//             
//             string stats = "=== Edge Case Controllers Performance ===\n";
//             
//             if (speedController != null)
//             {
//                 var speedBreakdown = speedController.GetSpeedBreakdown();
//                 stats += $"Speed: {speedBreakdown}\n";
//             }
//             
//             if (inputProcessor != null)
//             {
//                 var inputStats = inputProcessor.GetProcessingStats();
//                 stats += $"Input: {inputStats}\n";
//             }
//             
//             if (rotationController != null)
//             {
//                 var rotationStatus = rotationController.GetRotationStatus();
//                 stats += $"Rotation: {rotationStatus}\n";
//             }
//             
//             if (enableDebugMode)
//             {
//                 Debug.Log(stats);
//             }
//         }
//         
//         #endregion
//         
//         #region Public API
//         
//         /// <summary>
//         /// Get current movement speed (for external systems)
//         /// </summary>
//         public float GetCurrentMovementSpeed()
//         {
//             return speedController?.GetCurrentSpeed() ?? 5f;
//         }
//         
//         /// <summary>
//         /// Get processed input (for external systems)
//         /// </summary>
//         public Vector2 GetProcessedInput()
//         {
//             return inputProcessor?.GetProcessedInput() ?? Vector2.zero;
//         }
//         
//         /// <summary>
//         /// Get current rotation angle (for external systems)
//         /// </summary>
//         public float GetCurrentRotationAngle()
//         {
//             return rotationController?.GetCurrentRotationAngle() ?? 0f;
//         }
//         
//         /// <summary>
//         /// Check if any controller is optimizing for performance
//         /// </summary>
//         public bool IsOptimizing()
//         {
//             // Add optimization checks here when needed
//             return false;
//         }
//         
//         /// <summary>
//         /// Force update all controllers (for testing)
//         /// </summary>
//         public void ForceUpdateAllControllers()
//         {
//             speedController?.ForceSpeedUpdate();
//             inputProcessor?.ForceProcessInput();
//             rotationController?.ForceUpdate();
//         }
//         
//         /// <summary>
//         /// Reset all controllers to default state
//         /// </summary>
//         public void ResetAllControllers()
//         {
//             inputProcessor?.ResetInputState();
//             rotationController?.ResetToNeutral();
//             // Speed controller resets automatically based on context
//         }
//         
//         #endregion
//         
//         #region Debug Helpers
//         
//         [ContextMenu("Test Speed Controller")]
//         private void TestSpeedController()
//         {
//             if (speedController != null)
//             {
//                 var breakdown = speedController.GetSpeedBreakdown();
//                 Debug.Log($"Speed Controller Test: {breakdown}");
//             }
//         }
//         
//         [ContextMenu("Test Input Processor")]
//         private void TestInputProcessor()
//         {
//             if (inputProcessor != null)
//             {
//                 var stats = inputProcessor.GetProcessingStats();
//                 Debug.Log($"Input Processor Test: {stats}");
//             }
//         }
//         
//         [ContextMenu("Test Rotation Controller")]
//         private void TestRotationController()
//         {
//             if (rotationController != null)
//             {
//                 var status = rotationController.GetRotationStatus();
//                 Debug.Log($"Rotation Controller Test: {status}");
//             }
//         }
//         
//         #endregion
//     }
// }
