﻿using NewAnimancer;
using DefaultNamespace.Mono.Interface;
using Module.Mono.Animancer.RealsticFemale;
using Sirenix.OdinInspector;

namespace Module.Mono.Modules
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using UnityEngine;
    using NewAnimancer;

    public class AnimationModuleWithStates<T,T1> : SerializedMonoBehaviour,
        IAnimationModuleWithStates<T,T1>
        where T : State where T1 : Enum
    {
        public bool CanUpdate { get; private set; }
        [SerializeField] private bool m_useAnimancer;
        [field:SerializeField] public int ControllerIndex { get; private set; }
        [field: SerializeField] public List<MainState> MainState { get; set; }
        [field: SerializeField] public T1 SubState { get; set; }

        [field: SerializeField] public bool HasRefreshRate { get; private set; }
        [field: SerializeField] public float RefreshRate { get; private set; }

        public Enum GetModuleSubState() => SubState;

        [field: SerializeField] public Dictionary<Enum, T> States { get; set; }

        [SerializeField] private HybridAnimancerComponent _Animancer;
        public NewAnimancer.HybridAnimancerComponent Animancer => _Animancer;

        [SerializeField] private CharacterParameters _Parameters;
        public CharacterParameters Parameters => _Parameters;

        public StateMachine<State> StateMachine { get; private set; }

        private void Awake()
        {
            StateMachine = new StateMachine<State>();
        }

        public void Initialize( bool useAnimancer)
        {
            m_useAnimancer = useAnimancer;
        }
        
        public void Initialize(int controllerIndex)
        {
            if (_Animancer == null)
            {
                _Animancer = GetComponent<HybridAnimancerComponent>();
                if (_Animancer == null)
                    Debug.LogError("AnimancerComponent component is missing!");
            }

            if (!m_useAnimancer)
            {
                // Stop all separate animations and return to the Animator Controller:
                _Animancer.Stop();

                // Fade out Animancer's Layer 0 over 0.25 seconds and return to the Animator Controller:
                _Animancer.Layers[0].StartFade(0, 0.25f);
            }
            else
            {
                // Initialize the state machine with the initial state if needed
                if (States.TryGetValue(SubState, out var initialState))
                {
                    StateMachine.SetState(initialState, 0.25f);
                }
            }
        }

        public void UpdateModule(MainState currentMainState, ref Enum currentSubState)
        {
            if(!m_useAnimancer)
                return;
            
            SubState = (T1)currentSubState;
            if (States.TryGetValue(currentSubState, out var state))
            {
                state.UpdateState();
            }
        }

        public void PlayAnimation(AnimationClip animationClip)
        {
            if (_Animancer != null)
            {
                _Animancer.Play(animationClip);
            }
        }

        public void PlayAnimationState(Enum stateName, float crossFadeDuration = 0,System.Object conditions = null)
        {

            if (_Animancer != null)
            {
                if (States.TryGetValue(stateName, out var state))
                {
                    if(!StateMachine.IsTheSameState(state))
                        StateMachine.TrySetState(state, crossFadeDuration, conditions);
                }
            }
        }

        public void PlayAnimation(AnimationClip animationClip, float crossFadeDuration)
        {
            if (_Animancer != null)
            {
                _Animancer.Play(animationClip, crossFadeDuration, FadeMode.FixedDuration);
            }
        }

        public void PlayAnimation(string animationName)
        {
            throw new NotImplementedException();
        }

        public void PlayAnimation(string animationName, float crossFadeDuration)
        {
            throw new NotImplementedException();
        }

        public bool IsAnimationPlaying(string animationName)
        {
            if (_Animancer == null) return false;

            return _Animancer.IsPlaying(animationName);
        }

        public IEnumerator WaitForAnimation(string animationName)
        {
            yield return new WaitUntil(() => Animancer.IsPlaying(animationName));
            yield return new WaitUntil(() => !IsAnimationPlaying(animationName));
        }

        public bool IsPlayingAnimation { get; private set; }

        public void SetFloat(string parameterName, float value, float dampTime, float deltaTime)
        {
            Animancer.Animator.SetFloat(parameterName, value, dampTime, deltaTime);
        }

        public void SetFloat(string parameterName, float value)
        {
            Animancer.Animator.SetFloat(parameterName, value);
        }

        public float GetFloat(string parameterName)
        {
            return 0;
        }

        public void SetBool(string parameterName, bool value)
        {
            Animancer.Animator.SetBool(parameterName, value);
        }

        public void SetTrigger(string parameterName)
        {
        }

        public void SetLayerWeight(string layerName, float value)
        {
        }
        
        public void PlayState(State characterState, float crossFadeDuration = 0)
        {
            if(!m_useAnimancer)
                return;
            
            if (StateMachine.CurrentState != null && !StateMachine.CurrentState.Equals(characterState))
            {
                StateMachine.CurrentState.OnExitState();
                StateMachine.SetState(characterState,crossFadeDuration);
                StateMachine.CurrentState.OnEnterState(crossFadeDuration);
            }

                
            StateMachine.CurrentState.UpdateState();
        }
    }
}