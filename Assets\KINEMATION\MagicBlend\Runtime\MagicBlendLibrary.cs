// Designed by KINEMATION, 2025.

using KINEMATION.KAnimationCore.Runtime.Core;
using KINEMATION.KAnimationCore.Runtime.Rig;

using System;
using System.Collections.Generic;
using KINEMATION.KAnimationCore.Runtime.Attributes;
using Unity.Collections;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;

namespace KINEMATION.MagicBlend.Runtime
{
    public struct AtomPose
    {
        public KTransform basePose;
        public KTransform overlayPose;
        public Quaternion localOverlayRotation;
        
        public float baseWeight;
        public float additiveWeight;
        public float localWeight;

        public static AtomPose Lerp(AtomPose a, AtomPose b, float alpha)
        {
            AtomPose outPose = new AtomPose();
            
            outPose.basePose = KTransform.Lerp(a.basePose, b.basePose, alpha);
            outPose.overlayPose = KTransform.Lerp(a.overlayPose, b.overlayPose, alpha);
            outPose.localOverlayRotation = Quaternion.Slerp(a.localOverlayRotation, b.localOverlayRotation, alpha);

            outPose.additiveWeight = Mathf.Lerp(a.additiveWeight, b.additiveWeight, alpha);
            outPose.baseWeight = Mathf.Lerp(a.baseWeight, b.baseWeight, alpha);
            outPose.localWeight = Mathf.Lerp(a.localWeight, b.localWeight, alpha);
            
            return outPose;
        }
    }
    
    public struct BlendStreamAtom
    {
        [Unity.Collections.ReadOnly] public TransformStreamHandle handle;
        [Unity.Collections.ReadOnly] public float baseWeight;
        [Unity.Collections.ReadOnly] public float additiveWeight;
        [Unity.Collections.ReadOnly] public float localWeight;
        
        public KTransform meshStreamPose;
        public AtomPose activePose;
        public AtomPose cachedPose;
        
        public AtomPose GetBlendedAtomPose(float blendWeight)
        {
            return AtomPose.Lerp(cachedPose, activePose, blendWeight);
        }
    }

    [Serializable]
    public struct LayeredBlend
    {
        [CustomElementChainDrawer(false, true)]
        public KRigElementChain layer;
        [Range(0f, 1f)] public float baseWeight;
        [Range(0f, 1f)] public float additiveWeight;
        [Range(0f, 1f)] public float localWeight;
    }

    [Serializable]
    public struct OverrideOverlay
    {
        public AnimationClip overlay;
        public AvatarMask mask;
        [Range(0f, 1f)] public float weight;
    }
    
    public class MagicBlendLibrary
    {
        public static NativeArray<BlendStreamAtom> SetupBlendAtoms(Animator animator, KRigComponent rigComponent)
        {
            var bones = rigComponent.GetRigTransforms();
            
            int num = bones.Length;
            var blendAtoms = new NativeArray<BlendStreamAtom>(num, Allocator.Persistent);
            for (int i = 0; i < num; i++)
            {
                Transform bone = bones[i];
                blendAtoms[i] = new BlendStreamAtom()
                {
                    handle = animator.BindStreamTransform(bone)
                };
            }

            return blendAtoms;
        }

        // Connects an AnimationClip to an AnimationScriptPlayable.  
        // If the playable is already connected to the *exact* same clip (and speed is unchanged) we early-out to
        // avoid destroying/re-creating playables every frame which was causing visible hitches when the user kept
        // calling UpdateMagicBlendAsset with the same asset.  
        //
        // NOTE: **Keep this extremely lightweight** – we only compare the base clip and its speed. If the caller
        // wants to force a reconnection, they should pass a different clip instance or a different speed value.
        public static void ConnectPose(AnimationScriptPlayable playable, PlayableGraph graph, AnimationClip pose,
            float speed = 0f)
        {
            // Early-out if we are already connected to the requested clip with the same speed --------------------
            if (playable.GetInputCount() > 0 && playable.GetInput(0).IsValid())
            {
                var currentInput = playable.GetInput(0);
                if (currentInput.GetPlayableType() == typeof(AnimationClipPlayable))
                {
                    var currentClipPlayable = (AnimationClipPlayable)currentInput;
                    if (currentClipPlayable.GetAnimationClip() == pose &&
                        Mathf.Approximately((float)currentClipPlayable.GetSpeed(), speed))
                    {
                        // Already in the desired state – just return.
                        return;
                    }
                }
            }

            Playable oldInputPlayable = Playable.Null;
            if (playable.GetInputCount() > 0 && playable.GetInput(0).IsValid())
            {
                oldInputPlayable = playable.GetInput(0);
                playable.DisconnectInput(0);
                if (oldInputPlayable.IsValid())
                {
                    oldInputPlayable.Destroy();
                }
            }

            if (pose == null) // Guard against null pose
            {
                // If oldInputPlayable was valid, it's already destroyed by this point by the logic above.
                // Ensure the script playable has no inputs if the new pose is null.
                if(playable.GetInputCount() > 0) playable.SetInputCount(0);
                return;
            }

            var newPosePlayable = AnimationClipPlayable.Create(graph, pose);
            newPosePlayable.SetSpeed(speed);
            newPosePlayable.SetApplyFootIK(false);
            
            // Attempt to connect
            playable.SetInputCount(1); // Ensure input port exists
            playable.ConnectInput(0, newPosePlayable, 0); // Using instance method
            playable.SetInputWeight(0, 1f);

            // Verify connection
            if (playable.GetInput(0).GetHandle() != newPosePlayable.GetHandle())
            {
                Debug.LogError("[MagicBlendLibrary.ConnectPose] Failed to connect new pose playable. Destroying to prevent orphan.");
                if (newPosePlayable.IsValid()) newPosePlayable.Destroy();
                // Attempt to restore old connection if possible, or clear if not.
                // For simplicity now, we'll just ensure it's clean if connection failed.
                // If oldInputPlayable was valid and we failed to connect new, oldInputPlayable is already destroyed.
                // So, we effectively have no input connected if new one fails.
                playable.SetInputCount(0); // Set to 0 if new connection failed
            }
            // else: Connection successful, newPosePlayable is now the input.
        }
        
        // Connects an overlay setup (base + optional override clips) to an AnimationScriptPlayable.  
        // Similar to ConnectPose, but supports an AnimationLayerMixerPlayable when overrides are provided.  
        // The function tries to *reuse* the existing playable hierarchy whenever possible to avoid GC spikes and
        // animation hitches. If the currently connected tree matches the requested pose/overrides we simply update
        // the input weights & speed and exit early.  
        public static void ConnectOverlays(AnimationScriptPlayable playable, PlayableGraph graph,
            AnimationClip pose, List<OverrideOverlay> overrides, float speed = 0f)
        {
            // Null pose means clear connection ------------------------------------------------------------------
            if (pose == null)
            {
                if (playable.GetInputCount() > 0) playable.SetInputCount(0);
                return;
            }

            // No overrides? delegate to ConnectPose -------------------------------------------------------------
            if (overrides == null || overrides.Count == 0)
            {
                ConnectPose(playable, graph, pose, speed);
                return;
            }

            // EARLY-OUT CHECK ------------------------------------------------------------------------------------
            bool treeMatches = false;
            if (playable.GetInputCount() > 0 && playable.GetInput(0).IsValid() &&
                playable.GetInput(0).GetPlayableType() == typeof(AnimationLayerMixerPlayable))
            {
                var mixer = (AnimationLayerMixerPlayable)playable.GetInput(0);
                if (mixer.GetInputCount() == overrides.Count + 1) // base + overrides
                {
                    // Check base clip + speed
                    if (mixer.GetInput(0).IsValid() && mixer.GetInput(0).GetPlayableType() == typeof(AnimationClipPlayable))
                    {
                        var baseClipPlayable = (AnimationClipPlayable)mixer.GetInput(0);
                        if (baseClipPlayable.GetAnimationClip() == pose &&
                            Mathf.Approximately((float)baseClipPlayable.GetSpeed(), speed))
                        {
                            treeMatches = true;

                            // Validate override clips
                            for (int i = 0; i < overrides.Count; i++)
                            {
                                var inputIdx = i + 1;
                                var inputPlayable = mixer.GetInput(inputIdx);
                                if (!inputPlayable.IsValid() || inputPlayable.GetPlayableType() != typeof(AnimationClipPlayable))
                                {
                                    treeMatches = false; break;
                                }
                                var oPlayable = (AnimationClipPlayable)inputPlayable;
                                if (oPlayable.GetAnimationClip() != overrides[i].overlay)
                                {
                                    treeMatches = false; break;
                                }
                            }
                        }
                    }
                }

                if (treeMatches)
                {
                    // Update speeds & weights if needed then exit ---------------------------------------------
                    ((AnimationClipPlayable)mixer.GetInput(0)).SetSpeed(speed);
                    for (int i = 0; i < overrides.Count; i++)
                    {
                        var inputIdx = i + 1;
                        var clipPlayable = (AnimationClipPlayable)mixer.GetInput(inputIdx);
                        clipPlayable.SetSpeed(speed);
                        mixer.SetInputWeight(inputIdx, overrides[i].weight);
                    }
                    return;
                }
            }

            // REBUILD TREE ---------------------------------------------------------------------------------------
            var newMixerPlayable = AnimationLayerMixerPlayable.Create(graph);
            
            var baseOverlayClipPlayable = AnimationClipPlayable.Create(graph, pose);
            baseOverlayClipPlayable.SetDuration(pose.length);
            baseOverlayClipPlayable.SetSpeed(speed);
            baseOverlayClipPlayable.SetApplyFootIK(false);
            
            newMixerPlayable.AddInput(baseOverlayClipPlayable, 0, 1f);
            
            foreach (var overlayOverride in overrides)
            {
                if (overlayOverride.overlay == null) continue; // Skip null override clips
                var overrideClipPlayable = AnimationClipPlayable.Create(graph, overlayOverride.overlay);
                overrideClipPlayable.SetDuration(overlayOverride.overlay.length);
                overrideClipPlayable.SetSpeed(speed);
                overrideClipPlayable.SetApplyFootIK(false);
                
                var index = newMixerPlayable.AddInput(overrideClipPlayable, 0, overlayOverride.weight);
                var mask = overlayOverride.mask == null ? new AvatarMask() : overlayOverride.mask;
                
                newMixerPlayable.SetLayerMaskFromAvatarMask((uint) index, mask);
            }
            
            // Attempt to connect the new mixer
            playable.SetInputCount(1); // Ensure input port exists
            playable.ConnectInput(0, newMixerPlayable, 0); // Using instance method
            playable.SetInputWeight(0, 1f);

            // Verify connection
            if (playable.GetInput(0).GetHandle() != newMixerPlayable.GetHandle())
            {
                Debug.LogError("[MagicBlendLibrary.ConnectOverlays] Failed to connect new mixer playable. Destroying to prevent orphan.");
                if (newMixerPlayable.IsValid()) newMixerPlayable.Destroy(); // This should also destroy its inputs
                playable.SetInputCount(0); // Set to 0 if new connection failed
            }
            // else: Connection successful, newMixerPlayable is now the input.
        }
    }
}