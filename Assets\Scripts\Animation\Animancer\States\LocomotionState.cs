using System;
using System.Collections.Generic;
using Module.Mono.Animancer.RealsticFemale;
using NewAnimancer;
using NewAnimancer.Units;
using PlayerFAP._Mono;
using UnityEngine;
using AnimatedFloat = NewAnimancer.AnimatedFloat;
using ClipTransition = NewAnimancer.ClipTransition;
using MixerTransition2D = NewAnimancer.MixerTransition2D;

public sealed class LocomotionState : CharacterState
{
    [Serializable] 
    public struct MixerData
    {
        [SerializeField] public TransitionAssetBase BlendTree;
        [SerializeField] public StringAsset _ParameterX;
        [SerializeField] public StringAsset _ParameterY;
        [SerializeField] public SmoothedVector2Parameter _SmoothedParameters;
    }
    
    [Serializable]
    public struct MovementMixers
    {
        [SerializeField] public MixerData StartWalkingBlendTree;
        [SerializeField] public MixerData WalkingBlendTree;
        [SerializeField, Seconds] public float _ParameterSmoothTime;
    }

    [Serializable]
    public struct WeaponMovementMixers
    {
        [SerializeField] public WeaponSubModuleState subModuleState;
        [SerializeField] public MixerTransition2D blendTree;
    }

    [SerializeField] private float vertical;
    [SerializeField] private float horizontal;
    [SerializeField] private float verticalDump = .1f;
    [SerializeField] private float horizontalDump = .1f;

    [SerializeField] private float inputAngle;
    [SerializeField] private float inputMagnitude;
    [SerializeField] private float inputMagnitudeDamp = 0.1f;
    [SerializeField] private float InputAngleDump;


    [SerializeField] private float InputAngleDumpA = 3;
    [SerializeField] private float InputAngleDumpB = 0.8f;
    [SerializeField] private float InputAngleDumpT = 0.8f;

    [Header("Debug")] [SerializeField] private Vector2 m_debugParameterVector;

    public bool m_isMoving;

    [SerializeField] private float m_fadeTime;
    /************************************************************************************************************************/
    [SerializeField] private RuntimeAnimatorController m_NormalMovementAnimator;
    [SerializeField] private MovementMixers m_normalWalkingBlendTree;
    [SerializeField] private MovementMixers m_strufWalkingBlendTree;
    [SerializeField] private List<WeaponMovementMixers> m_weaponMovementMixers;

    [SerializeField] private bool _canUpdate;
    private AnimatedFloat _FootFall;

    [SerializeField] private Float1Controller m_float1Controller;
    [SerializeField] private FootstepEventsAnimation m_footstepEventsAnimation;
    public override bool CanEnterState => Character.Parameters.IsGrounded;

    // Advanced animation parameters
    [Header("Animation Tuning")] [SerializeField]
    private float normalMovementResponseTime = 0.12f;

    [SerializeField] private float aimingMovementResponseTime = 0.08f;
    [SerializeField] private float startMovementBlendTime = 0.15f;
    [SerializeField] private float directionChangeBlendTime = 0.2f;
    [SerializeField] private float magnitudeChangeBlendTime = 0.1f;
    [SerializeField] private AnimationCurve directionChangeCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

    // Footstep timing improvement
    [SerializeField] private float footstepSpeedInfluence = 0.2f;

    // Last frame values for interpolation
    private float lastInputAngle;
    private float lastInputMagnitude;
    private float lastHorizontal;
    private float lastVertical;

    [field: SerializeField]
    public Dictionary<WeaponSubModuleState, Dictionary<WeaponSubState, ClipTransition>> weaponsAnimations
    {
        get;
        private set;
    }

    private void Awake()
    {
        m_normalWalkingBlendTree.StartWalkingBlendTree.BlendTree.Events.OnEnd =
            () => { _canUpdate = true; };

        m_strufWalkingBlendTree.StartWalkingBlendTree.BlendTree.Events.OnEnd =
            () => { _canUpdate = true; };

        m_float1Controller = new Float1Controller();
        m_float1Controller.ZeroClampThreshold = 0.01f; // Adjust this threshold based on your needs
        m_float1Controller.DampingTime = 0.1f; // Adjust the damping time as needed
        m_float1Controller.UseLinearInterpolation =
            false; // Start with SmoothDamp and switch to true for Lerp if needed
        
        m_normalWalkingBlendTree.StartWalkingBlendTree._SmoothedParameters = new SmoothedVector2Parameter(
            Character.Animancer,
            m_normalWalkingBlendTree.StartWalkingBlendTree._ParameterX,
            m_normalWalkingBlendTree.StartWalkingBlendTree._ParameterY,
            m_normalWalkingBlendTree._ParameterSmoothTime);
        
        m_normalWalkingBlendTree.WalkingBlendTree._SmoothedParameters = new SmoothedVector2Parameter(
            Character.Animancer,
            m_normalWalkingBlendTree.WalkingBlendTree._ParameterX,
            m_normalWalkingBlendTree.WalkingBlendTree._ParameterY,
            m_normalWalkingBlendTree._ParameterSmoothTime);
        
        m_strufWalkingBlendTree.StartWalkingBlendTree._SmoothedParameters = new SmoothedVector2Parameter(
            Character.Animancer,
            m_strufWalkingBlendTree.StartWalkingBlendTree._ParameterX,
            m_strufWalkingBlendTree.StartWalkingBlendTree._ParameterY,
            m_strufWalkingBlendTree._ParameterSmoothTime);
        
        m_strufWalkingBlendTree.WalkingBlendTree._SmoothedParameters = new SmoothedVector2Parameter(
            Character.Animancer,
            m_strufWalkingBlendTree.WalkingBlendTree._ParameterX,
            m_strufWalkingBlendTree.WalkingBlendTree._ParameterY,
            m_strufWalkingBlendTree._ParameterSmoothTime);
        
    }

    public StateMachine<CharacterState> StateMachine { get; }

    public override void OnEnterState(float fadeTime, System.Object conditions = null)
    {
        m_fadeTime = fadeTime;
        _canUpdate = false;
        if (!Character.Parameters.IsAiming)
        {
            // Initialize the input values
            InputAngleDump = InputAngleDumpA;
            inputMagnitude = Character.Parameters.InputMagnitude.Value;

            //Character.Animancer.runtimeAnimatorController = m_NormalMovementAnimator;
            //Character.Animancer.Animator.runtimeAnimatorController = m_NormalMovementAnimator;

            // Character.Animancer.Animator.SetFloat("WalkStartAngle", Character.Parameters.StartWalkAngle.Value);
            // Character.Animancer.Animator.SetFloat("InputAngle", Character.Parameters.InputAngle.Value);
            // Character.Animancer.Animator.SetFloat("InputMagnitude", Character.Parameters.InputMagnitude.Value, 0.12f,
            //     Time.deltaTime);

            Character.Animancer.Layers[0].StartFade(1, .1f);
            Character.Animancer.Play(m_normalWalkingBlendTree.StartWalkingBlendTree.BlendTree, fadeTime);
            m_debugParameterVector = new Vector2(Character.Parameters.StartWalkAngle.Value,
                Character.Parameters.InputMagnitude.Value);

            m_normalWalkingBlendTree.StartWalkingBlendTree._SmoothedParameters.TargetValue = m_debugParameterVector;
        }
        else
        {
            Character.Animancer.Layers[0].StartFade(1, .1f);
            Character.Animancer.Play(m_strufWalkingBlendTree.StartWalkingBlendTree.BlendTree, fadeTime);

            m_debugParameterVector =
                new Vector2(Character.Parameters.Horizontal.Value, Character.Parameters.Vertical.Value);
            
            m_strufWalkingBlendTree.StartWalkingBlendTree._SmoothedParameters.TargetValue = m_debugParameterVector;
        }
    }

    public override void UpdateState()
    {
        m_isMoving = true;

        // Calculate the change in direction from last frame
        float angleDifference = 0;
        if (!Character.Parameters.IsAiming)
        {
            angleDifference = Mathf.Abs(Mathf.DeltaAngle(lastInputAngle, Character.Parameters.InputAngle.Value));
        }
        else
        {
            // For strafing, calculate the vector change
            Vector2 lastInput = new Vector2(lastHorizontal, lastVertical);
            Vector2 currentInput =
                new Vector2(Character.Parameters.Horizontal.Value, Character.Parameters.Vertical.Value);
            angleDifference = Vector2.Angle(lastInput, currentInput);
        }

        // Adjust blend times based on direction change magnitude
        float directionBlendFactor = directionChangeCurve.Evaluate(Mathf.Clamp01(angleDifference / 180f));
        float currentDirectionBlendTime =
            Mathf.Lerp(normalMovementResponseTime, directionChangeBlendTime, directionBlendFactor);

        // Initial animation setup during transition
        if (!_canUpdate)
        {
            if (!Character.Parameters.IsAiming)
            {
                inputAngle = Mathf.Lerp(inputAngle, Character.Parameters.InputAngle.Value,
                    Time.deltaTime / currentDirectionBlendTime);
                inputMagnitude = Mathf.Lerp(inputMagnitude, Character.Parameters.InputMagnitude.Value,
                    Time.deltaTime / magnitudeChangeBlendTime);
            }

            return;
        }

        // Update animation parameters based on movement type
        if (!Character.Parameters.IsAiming)
        {
            // Normal movement (non-aiming)
            Character.Animancer.Layers[0].StartFade(1, startMovementBlendTime);
            Character.Animancer.Play(m_normalWalkingBlendTree.WalkingBlendTree.BlendTree);

            // Calculate adaptive response times based on movement changes
            float speedDifference = Mathf.Abs(lastInputMagnitude - Character.Parameters.InputMagnitude.Value);
            float magnitudeBlendTime = Mathf.Lerp(inputMagnitudeDamp, magnitudeChangeBlendTime, speedDifference);

            // Apply smoothing with adaptive response times
            InputAngleDump = Mathf.Lerp(InputAngleDumpA, InputAngleDumpB, Time.deltaTime / InputAngleDumpT);
            inputAngle = Mathf.Lerp(inputAngle, Character.Parameters.InputAngle.Value,
                Time.deltaTime / currentDirectionBlendTime);
            inputMagnitude = Mathf.Lerp(inputMagnitude, Character.Parameters.InputMagnitude.Value,
                Time.deltaTime / magnitudeBlendTime);

            // Update animation parameters
            m_debugParameterVector = new Vector2(inputAngle, inputMagnitude);
            m_normalWalkingBlendTree.WalkingBlendTree._SmoothedParameters.TargetValue = m_debugParameterVector;
            
            // Store values for next frame
            lastInputAngle = inputAngle;
            lastInputMagnitude = inputMagnitude;
        }
        else
        {
            // Aiming movement (strafing)
            Character.Animancer.Play(m_strufWalkingBlendTree.WalkingBlendTree.BlendTree, m_fadeTime);

            // Calculate adaptive response times for strafing
            Vector2 currentInput =
                new Vector2(Character.Parameters.Horizontal.Value, Character.Parameters.Vertical.Value);
            Vector2 lastInput = new Vector2(horizontal, vertical);
            float inputChangeMagnitude = Vector2.Distance(lastInput, currentInput);

            // Adjust response times based on input change
            float horizontalResponseTime = Mathf.Lerp(horizontalDump, aimingMovementResponseTime, inputChangeMagnitude);
            float verticalResponseTime = Mathf.Lerp(verticalDump, aimingMovementResponseTime, inputChangeMagnitude);

            // Apply smoothing with adaptive response times
            horizontal = Mathf.Lerp(horizontal, Character.Parameters.Horizontal.Value,
                Time.deltaTime / horizontalResponseTime);
            vertical = Mathf.Lerp(vertical, Character.Parameters.Vertical.Value, Time.deltaTime / verticalResponseTime);

            // Update animation parameters
            m_debugParameterVector = new Vector2(horizontal, vertical);
            m_strufWalkingBlendTree.WalkingBlendTree._SmoothedParameters.TargetValue = m_debugParameterVector;

            // Store values for next frame
            lastHorizontal = horizontal;
            lastVertical = vertical;
        }

        // Update footstep timing based on movement speed
        if (m_footstepEventsAnimation != null)
        {
            float speedFactor = Character.Parameters.InputMagnitude.Value;
            m_footstepEventsAnimation.SetSpeedMultiplier(1f + (speedFactor * footstepSpeedInfluence));
        }
    }

   
    public override void OnExitState()
    {
        base.OnExitState();
        m_isMoving = false;
        _canUpdate = false;
    }

    /************************************************************************************************************************/

    private void UpdateRotation()
    {
        // If the default locomotion state is not active we must be performing a quick turn.
        // Those animations use root motion to perform the turn so we don't want any scripted rotation during them.
    }

    /************************************************************************************************************************/

    [SerializeField] private NewAnimancer.UnityEvent _PlayFootstepAudio; // See the Read Me.

    private bool _CanPlayAudio;
    private bool _IsPlayingAudio;

    // This is the same logic used for locomotion audio in the original PlayerController.
    private void UpdateAudio()
    {
        const float Threshold = 0.01f;

        var footFallCurve = _FootFall.Value;
        if (footFallCurve > Threshold && !_IsPlayingAudio && _CanPlayAudio)
        {
            _IsPlayingAudio = true;
            _CanPlayAudio = false;

            _PlayFootstepAudio.Invoke();
        }
        else if (_IsPlayingAudio)
        {
            _IsPlayingAudio = false;
        }
        else if (footFallCurve < Threshold && !_CanPlayAudio)
        {
            _CanPlayAudio = true;
        }
    }

    public class Float1Controller
    {
        private float currentValue;
        private float targetValue;
        private float currentVelocity;

        public float Parameter
        {
            get => currentValue;
            set => targetValue = value;
        }

        public float DampingTime { get; set; }
        public float ZeroClampThreshold { get; set; } = 0.01f; // Threshold for clamping to zero
        public bool UseLinearInterpolation { get; set; } = false; // Option to use linear interpolation

        public Float1Controller(float initialValue = 0, float dampingTime = 0.1f)
        {
            currentValue = initialValue;
            targetValue = initialValue;
            DampingTime = dampingTime;
        }

        public void Update(float deltaTime)
        {
            if (UseLinearInterpolation)
            {
                currentValue = Mathf.Lerp(currentValue, targetValue, deltaTime / DampingTime);
            }
            else
            {
                currentValue = Mathf.SmoothDamp(currentValue, targetValue, ref currentVelocity, DampingTime,
                    Mathf.Infinity, deltaTime);
            }

            // Clamp to zero if within the threshold
            if (Mathf.Abs(currentValue) < ZeroClampThreshold)
            {
                currentValue = 0f;
                currentVelocity = 0f;
            }
        }
    }
}