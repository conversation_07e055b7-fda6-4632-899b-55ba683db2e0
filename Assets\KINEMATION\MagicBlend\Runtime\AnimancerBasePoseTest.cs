using KINEMATION.MagicBlend.Runtime;
using MagicBlendAnimancerIntegration;
using NewAnimancer;
using UnityEngine;
using MagicBlendState = MagicBlendAnimancerIntegration.MagicBlendState;

/// <summary>
/// Test script specifically for testing Animancer base pose integration with MagicBlend.
/// This tests the useAnimancerAsBaseInput functionality to ensure animated base poses work correctly.
/// </summary>
public class AnimancerBasePoseTest : MonoBehaviour
{
    [Header("Required Components")]
    [SerializeField] private HybridAnimancerComponent animancer;
    [SerializeField] private MagicBlending magicBlending;
    
    [Header("Test Assets")]
    [SerializeField] private AnimationClip baseAnimation; // The animation to play as base
    [SerializeField] private MagicBlendAsset magicBlendAsset; // The MagicBlend asset to overlay
    
    [Header("Settings")]
    [SerializeField] private float fadeTime = 0.25f;
    [SerializeField] private bool debugMode = true;
    
    private AnimancerState currentBaseState;
    private MagicBlendState currentMagicState;

    private void Start()
    {
        if (animancer == null)
            animancer = GetComponent<HybridAnimancerComponent>();
            
        if (magicBlending == null)
            magicBlending = GetComponent<MagicBlending>();
        
        if (debugMode)
        {
            Debug.Log("[AnimancerBasePoseTest] Test initialized");
            Debug.Log($"[AnimancerBasePoseTest] useAnimancerAsBaseInput: {magicBlending?.useAnimancerAsBaseInput}");
        }
    }

    private void Update()
    {
        // Test 1: Play base animation only
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            TestBaseAnimationOnly();
        }
        
        // Test 2: Play MagicBlend with static base pose
        if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            TestMagicBlendStaticBase();
        }
        
        // Test 3: Play MagicBlend with animated base pose
        if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            TestMagicBlendAnimatedBase();
        }
        
        // Test 4: Toggle useAnimancerAsBaseInput flag
        if (Input.GetKeyDown(KeyCode.Alpha4))
        {
            ToggleAnimancerBaseInput();
        }
        
        // Test 5: Refresh connections
        if (Input.GetKeyDown(KeyCode.Alpha5))
        {
            RefreshConnections();
        }
    }

    private void TestBaseAnimationOnly()
    {
        if (baseAnimation == null)
        {
            Debug.LogWarning("[AnimancerBasePoseTest] No base animation assigned!");
            return;
        }

        try
        {
            currentBaseState = animancer.Play(baseAnimation, fadeTime);
            Debug.Log($"[AnimancerBasePoseTest] ✅ Base animation playing: {baseAnimation.name}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[AnimancerBasePoseTest] ❌ Base animation failed: {e.Message}");
        }
    }

    private void TestMagicBlendStaticBase()
    {
        if (magicBlendAsset == null)
        {
            Debug.LogWarning("[AnimancerBasePoseTest] No MagicBlend asset assigned!");
            return;
        }

        try
        {
            // Ensure we're using static base pose
            if (magicBlending != null)
            {
                magicBlending.useAnimancerAsBaseInput = false;
                magicBlending.RefreshAssetConnections();
            }
            
            currentMagicState = animancer.Play(magicBlendAsset, fadeTime);
            Debug.Log($"[AnimancerBasePoseTest] ✅ MagicBlend with static base: {magicBlendAsset.name}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[AnimancerBasePoseTest] ❌ MagicBlend static base failed: {e.Message}");
        }
    }

    private void TestMagicBlendAnimatedBase()
    {
        if (magicBlendAsset == null || baseAnimation == null)
        {
            Debug.LogWarning("[AnimancerBasePoseTest] Missing assets for animated base test!");
            return;
        }

        try
        {
            // First play the base animation
            currentBaseState = animancer.Play(baseAnimation, fadeTime);
            
            // Enable animated base input
            if (magicBlending != null)
            {
                magicBlending.useAnimancerAsBaseInput = true;
                magicBlending.RefreshAssetConnections();
            }
            
            // Then play MagicBlend which should use the animated base
            currentMagicState = animancer.Play(magicBlendAsset, fadeTime);
            
            Debug.Log($"[AnimancerBasePoseTest] ✅ MagicBlend with animated base: {baseAnimation.name} + {magicBlendAsset.name}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[AnimancerBasePoseTest] ❌ MagicBlend animated base failed: {e.Message}");
        }
    }

    private void ToggleAnimancerBaseInput()
    {
        if (magicBlending == null)
        {
            Debug.LogWarning("[AnimancerBasePoseTest] No MagicBlending component found!");
            return;
        }

        magicBlending.useAnimancerAsBaseInput = !magicBlending.useAnimancerAsBaseInput;
        magicBlending.RefreshAssetConnections();
        
        Debug.Log($"[AnimancerBasePoseTest] Toggled useAnimancerAsBaseInput to: {magicBlending.useAnimancerAsBaseInput}");
    }

    private void RefreshConnections()
    {
        if (magicBlending == null)
        {
            Debug.LogWarning("[AnimancerBasePoseTest] No MagicBlending component found!");
            return;
        }

        magicBlending.RefreshAssetConnections();
        Debug.Log("[AnimancerBasePoseTest] Refreshed MagicBlend connections");
    }

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 350, 250));
        GUILayout.Label("Animancer Base Pose Test", GUI.skin.box);
        
        if (GUILayout.Button("1 - Play Base Animation Only"))
        {
            TestBaseAnimationOnly();
        }
        
        if (GUILayout.Button("2 - MagicBlend (Static Base)"))
        {
            TestMagicBlendStaticBase();
        }
        
        if (GUILayout.Button("3 - MagicBlend (Animated Base)"))
        {
            TestMagicBlendAnimatedBase();
        }
        
        if (GUILayout.Button("4 - Toggle Animancer Base Input"))
        {
            ToggleAnimancerBaseInput();
        }
        
        if (GUILayout.Button("5 - Refresh Connections"))
        {
            RefreshConnections();
        }
        
        GUILayout.Space(10);
        
        // Status display
        GUILayout.Label("Status:", GUI.skin.label);
        GUILayout.Label($"Current State: {(animancer?.States.Current?.ToString() ?? "None")}");
        GUILayout.Label($"Use Animancer Base: {(magicBlending?.useAnimancerAsBaseInput.ToString() ?? "N/A")}");
        GUILayout.Label($"Base Animation: {(baseAnimation?.name ?? "None")}");
        GUILayout.Label($"MagicBlend Asset: {(magicBlendAsset?.name ?? "None")}");
        
        GUILayout.EndArea();
    }
}
