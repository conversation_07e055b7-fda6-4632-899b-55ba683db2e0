%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8462350007573319849
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftHandRing1
    elementChain:
    - name: LeftHandRing1
      index: 37
      isVirtual: 0
    - name: LeftHandRing2
      index: 38
      isVirtual: 0
    - name: LeftHandRing3
      index: 39
      isVirtual: 0
    - name: LeftHandRing4
      index: 40
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-7860176876909792579
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftHandPinky1
    elementChain:
    - name: LeftHandPinky1
      index: 32
      isVirtual: 0
    - name: LeftHandPinky2
      index: 33
      isVirtual: 0
    - name: LeftHandPinky3
      index: 34
      isVirtual: 0
    - name: LeftHandPinky4
      index: 35
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-7553092928073225803
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: thigh_l
    elementChain:
    - name: thigh_l
      index: 61
      isVirtual: 0
    - name: calf_l
      index: 62
      isVirtual: 0
    - name: foot_l
      index: 64
      isVirtual: 0
    - name: ball_l
      index: 65
      isVirtual: 0
  targetChain:
    chainName: LeftUpLeg
    elementChain:
    - name: LeftUpLeg
      index: 2
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-6230522917364375654
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightForeArm
    elementChain:
    - name: RightForeArm
      index: 50
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-6053893547397402418
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: root
    elementChain:
    - name: root
      index: 0
      isVirtual: 0
    - name: pelvis
      index: 1
      isVirtual: 0
  targetChain:
    chainName: LeftFoot
    elementChain:
    - name: LeftFoot
      index: 4
      isVirtual: 0
    - name: LeftToeBase
      index: 5
      isVirtual: 0
    - name: LeftToeBase_END
      index: 6
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-6023297538690974644
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftForeArm
    elementChain:
    - name: LeftForeArm
      index: 21
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-5656072972020838869
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightHandIndex1
    elementChain:
    - name: RightHandIndex1
      index: 53
      isVirtual: 0
    - name: RightHandIndex2
      index: 54
      isVirtual: 0
    - name: RightHandIndex3
      index: 55
      isVirtual: 0
    - name: RightHandIndex4
      index: 56
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-5585705316378015154
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftForeArmRoll
    elementChain:
    - name: LeftForeArmRoll
      index: 22
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-5348476352549506178
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightUpLegRoll
    elementChain:
    - name: RightUpLegRoll
      index: 15
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-4755341133115492341
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightFoot
    elementChain:
    - name: RightFoot
      index: 11
      isVirtual: 0
    - name: RightToeBase
      index: 12
      isVirtual: 0
    - name: RightToeBase_END
      index: 13
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-3944106260154457687
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: neck_01
    elementChain:
    - name: neck_01
      index: 49
      isVirtual: 0
    - name: head
      index: 50
      isVirtual: 0
    - name: CC_Base_FacialBone
      index: 51
      isVirtual: 0
  targetChain:
    chainName: Neck
    elementChain:
    - name: Neck
      index: 45
      isVirtual: 0
    - name: Head
      index: 46
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-3923588897271939600
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftArmRoll
    elementChain:
    - name: LeftArmRoll
      index: 20
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-3810599737529335713
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightArmRoll
    elementChain:
    - name: RightArmRoll
      index: 49
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-3000686755906253925
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftHand
    elementChain:
    - name: LeftHand
      index: 23
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-2565829691424316407
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightShoulder
    elementChain:
    - name: RightShoulder
      index: 47
      isVirtual: 0
    - name: RightArm
      index: 48
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-1836396083672616830
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightHand
    elementChain:
    - name: RightHand
      index: 52
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-1772223843142245376
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightUpLeg
    elementChain:
    - name: RightUpLeg
      index: 9
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-884185989324957206
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightLeg
    elementChain:
    - name: RightLeg
      index: 10
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-390093580890585461
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightHandMiddle1
    elementChain:
    - name: RightHandMiddle1
      index: 57
      isVirtual: 0
    - name: RightHandMiddle2
      index: 58
      isVirtual: 0
    - name: RightHandMiddle3
      index: 59
      isVirtual: 0
    - name: RightHandMiddle4
      index: 60
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &-178827086837155856
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightHandProp
    elementChain:
    - name: RightHandProp
      index: 65
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 12349bae48c61824bb8e519537ce28eb, type: 3}
  m_Name: Retarget_Rig_Female1_Rig_FemaleMovementAnimsetPro_1
  m_EditorClassIdentifier: 
  sourceCharacter: {fileID: 0}
  targetCharacter: {fileID: 0}
  sourcePose: {fileID: 0}
  targetPose: {fileID: 0}
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  excludeChain:
    chainName: Elements To Exclude
    elementChain: []
  retargetFeatures:
  - {fileID: 7706378840043793006}
  - {fileID: -7553092928073225803}
  - {fileID: 7844576755638627184}
  - {fileID: -6053893547397402418}
  - {fileID: 7868447811063145772}
  - {fileID: 5911297539318133298}
  - {fileID: -1772223843142245376}
  - {fileID: -884185989324957206}
  - {fileID: -4755341133115492341}
  - {fileID: 7424584251806023173}
  - {fileID: -5348476352549506178}
  - {fileID: 1265590534277018689}
  - {fileID: 507174811528559820}
  - {fileID: -3923588897271939600}
  - {fileID: -6023297538690974644}
  - {fileID: -5585705316378015154}
  - {fileID: -3000686755906253925}
  - {fileID: 2577063235644902084}
  - {fileID: 4922623737510323865}
  - {fileID: -7860176876909792579}
  - {fileID: 8633970878078590242}
  - {fileID: -8462350007573319849}
  - {fileID: 6724826683910532798}
  - {fileID: -3944106260154457687}
  - {fileID: -2565829691424316407}
  - {fileID: -3810599737529335713}
  - {fileID: -6230522917364375654}
  - {fileID: 4425072997135803252}
  - {fileID: -1836396083672616830}
  - {fileID: -5656072972020838869}
  - {fileID: -390093580890585461}
  - {fileID: 5916768532185078875}
  - {fileID: -178827086837155856}
  - {fileID: 320762884996674093}
  - {fileID: 1562625372669611582}
--- !u!114 &320762884996674093
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightHandRing1
    elementChain:
    - name: RightHandRing1
      index: 66
      isVirtual: 0
    - name: RightHandRing2
      index: 67
      isVirtual: 0
    - name: RightHandRing3
      index: 68
      isVirtual: 0
    - name: RightHandRing4
      index: 69
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &507174811528559820
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftShoulder
    elementChain:
    - name: LeftShoulder
      index: 18
      isVirtual: 0
    - name: LeftArm
      index: 19
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &1265590534277018689
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: spine_01
    elementChain:
    - name: spine_01
      index: 2
      isVirtual: 0
    - name: spine_02
      index: 3
      isVirtual: 0
    - name: spine_03
      index: 4
      isVirtual: 0
  targetChain:
    chainName: Spine
    elementChain:
    - name: Spine
      index: 16
      isVirtual: 0
    - name: Spine1
      index: 17
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &1562625372669611582
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightHandThumb1
    elementChain:
    - name: RightHandThumb1
      index: 70
      isVirtual: 0
    - name: RightHandThumb2
      index: 71
      isVirtual: 0
    - name: RightHandThumb3
      index: 72
      isVirtual: 0
    - name: RightHandThumb4
      index: 73
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &2577063235644902084
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftHandIndex1
    elementChain:
    - name: LeftHandIndex1
      index: 24
      isVirtual: 0
    - name: LeftHandIndex2
      index: 25
      isVirtual: 0
    - name: LeftHandIndex3
      index: 26
      isVirtual: 0
    - name: LeftHandIndex4
      index: 27
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &4425072997135803252
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightForeArmRoll
    elementChain:
    - name: RightForeArmRoll
      index: 51
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &4922623737510323865
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: middle_01_r
    elementChain:
    - name: middle_01_r
      index: 35
      isVirtual: 0
    - name: middle_02_r
      index: 36
      isVirtual: 0
    - name: middle_03_r
      index: 37
      isVirtual: 0
  targetChain:
    chainName: LeftHandMiddle1
    elementChain:
    - name: LeftHandMiddle1
      index: 28
      isVirtual: 0
    - name: LeftHandMiddle2
      index: 29
      isVirtual: 0
    - name: LeftHandMiddle3
      index: 30
      isVirtual: 0
    - name: LeftHandMiddle4
      index: 31
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &5911297539318133298
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftUpLegRoll
    elementChain:
    - name: LeftUpLegRoll
      index: 8
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &5916768532185078875
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightHandPinky1
    elementChain:
    - name: RightHandPinky1
      index: 61
      isVirtual: 0
    - name: RightHandPinky2
      index: 62
      isVirtual: 0
    - name: RightHandPinky3
      index: 63
      isVirtual: 0
    - name: RightHandPinky4
      index: 64
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &6724826683910532798
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftHandThumb1
    elementChain:
    - name: LeftHandThumb1
      index: 41
      isVirtual: 0
    - name: LeftHandThumb2
      index: 42
      isVirtual: 0
    - name: LeftHandThumb3
      index: 43
      isVirtual: 0
    - name: LeftHandThumb4
      index: 44
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &7424584251806023173
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: RightLegRoll
    elementChain:
    - name: RightLegRoll
      index: 14
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &7706378840043793006
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: CC_Base_JawRoot
    elementChain:
    - name: CC_Base_JawRoot
      index: 52
      isVirtual: 0
  targetChain:
    chainName: Root
    elementChain:
    - name: Root
      index: 0
      isVirtual: 0
    - name: Hips
      index: 1
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &7844576755638627184
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: thigh_l
    elementChain:
    - name: thigh_l
      index: 61
      isVirtual: 0
    - name: calf_l
      index: 62
      isVirtual: 0
    - name: foot_l
      index: 64
      isVirtual: 0
    - name: ball_l
      index: 65
      isVirtual: 0
  targetChain:
    chainName: LeftLeg
    elementChain:
    - name: LeftLeg
      index: 3
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &7868447811063145772
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftLegRoll
    elementChain:
    - name: LeftLegRoll
      index: 7
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
--- !u!114 &8633970878078590242
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: 217a549015c9afe42b46688714a6808b, type: 2}
  targetRig: {fileID: 11400000, guid: cf7c5c124e9df1541b29ed4544bf4225, type: 2}
  featureWeight: 1
  sourceChain:
    chainName: None
    elementChain: []
  targetChain:
    chainName: LeftHandProp
    elementChain:
    - name: LeftHandProp
      index: 36
      isVirtual: 0
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 0
  effectorOffset: {x: 0, y: 0, z: 0}
  poleOffset: {x: 0, y: 0, z: 0}
