using KINEMATION.MagicBlend.Runtime;
using MagicBlendAnimancerIntegration;
using NewAnimancer;
using UnityEngine;

/// <summary>
/// Example script showing how to use the fixed Animancer + MagicBlend integration.
/// This demonstrates both static and animated base pose workflows.
/// </summary>
public class AnimancerMagicBlendUsageExample : MonoBehaviour
{
    [Header("Required Components")]
    [SerializeField] private HybridAnimancerComponent animancer;
    [SerializeField] private MagicBlending magicBlending;
    
    [Header("Animation Assets")]
    [SerializeField] private AnimationClip walkAnimation;
    [SerializeField] private AnimationClip runAnimation;
    [SerializeField] private AnimationClip idleAnimation;
    
    [Header("MagicBlend Assets")]
    [SerializeField] private MagicBlendAsset aimingBlend;
    [SerializeField] private MagicBlendAsset reloadBlend;
    [SerializeField] private MagicBlendAsset injuredBlend;
    
    [Head<PERSON>("Settings")]
    [SerializeField] private float fadeTime = 0.25f;
    [SerializeField] private bool useAnimatedBasePose = true;

    private void Start()
    {
        // Get components if not assigned
        if (animancer == null)
            animancer = GetComponent<HybridAnimancerComponent>();
            
        if (magicBlending == null)
            magicBlending = GetComponent<MagicBlending>();
        
        // Configure MagicBlend for animated base poses
        if (magicBlending != null)
        {
            magicBlending.useAnimancerAsBaseInput = useAnimatedBasePose;
            Debug.Log($"[AnimancerMagicBlendExample] Configured for {(useAnimatedBasePose ? "animated" : "static")} base poses");
        }
    }

    private void Update()
    {
        HandleMovementInput();
        HandleActionInput();
    }

    private void HandleMovementInput()
    {
        // Basic movement animations
        if (Input.GetKey(KeyCode.W))
        {
            if (Input.GetKey(KeyCode.LeftShift))
            {
                PlayBaseAnimation(runAnimation, "Running");
            }
            else
            {
                PlayBaseAnimation(walkAnimation, "Walking");
            }
        }
        else if (Input.GetKeyUp(KeyCode.W) || Input.GetKeyUp(KeyCode.LeftShift))
        {
            PlayBaseAnimation(idleAnimation, "Idle");
        }
    }

    private void HandleActionInput()
    {
        // Action overlays using MagicBlend
        if (Input.GetKeyDown(KeyCode.Mouse1)) // Right click for aiming
        {
            PlayMagicBlendOverlay(aimingBlend, "Aiming");
        }
        
        if (Input.GetKeyDown(KeyCode.R)) // R for reload
        {
            PlayMagicBlendOverlay(reloadBlend, "Reloading");
        }
        
        if (Input.GetKeyDown(KeyCode.H)) // H for injured state
        {
            PlayMagicBlendOverlay(injuredBlend, "Injured");
        }
    }

    /// <summary>
    /// Plays a base animation through Animancer.
    /// When useAnimatedBasePose is true, this will feed into MagicBlend as the base pose.
    /// </summary>
    private void PlayBaseAnimation(AnimationClip clip, string actionName)
    {
        if (clip == null || animancer == null) return;

        try
        {
            var state = animancer.Play(clip, fadeTime);
            Debug.Log($"[AnimancerMagicBlendExample] Playing base animation: {actionName}");
            
            // If we have an active MagicBlend overlay and we're using animated base poses,
            // the overlay will automatically use this new base animation
            if (useAnimatedBasePose && magicBlending != null)
            {
                // Refresh connections to ensure the new base animation is used
                magicBlending.RefreshAssetConnections();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[AnimancerMagicBlendExample] Failed to play base animation {actionName}: {e.Message}");
        }
    }

    /// <summary>
    /// Plays a MagicBlend overlay on top of the current base animation.
    /// </summary>
    private void PlayMagicBlendOverlay(MagicBlendAsset asset, string actionName)
    {
        if (asset == null || animancer == null) return;

        try
        {
            var state = animancer.Play(asset, fadeTime);
            Debug.Log($"[AnimancerMagicBlendExample] Playing MagicBlend overlay: {actionName}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[AnimancerMagicBlendExample] Failed to play MagicBlend overlay {actionName}: {e.Message}");
        }
    }

    /// <summary>
    /// Switches between static and animated base pose modes at runtime.
    /// </summary>
    public void ToggleBasePoseMode()
    {
        if (magicBlending == null) return;

        useAnimatedBasePose = !useAnimatedBasePose;
        magicBlending.useAnimancerAsBaseInput = useAnimatedBasePose;
        magicBlending.RefreshAssetConnections();
        
        Debug.Log($"[AnimancerMagicBlendExample] Switched to {(useAnimatedBasePose ? "animated" : "static")} base pose mode");
    }

    /// <summary>
    /// Example of how to create and play a MagicBlend transition at runtime.
    /// </summary>
    public void PlayCustomMagicBlendTransition(MagicBlendAsset asset, float customFadeTime)
    {
        if (asset == null || animancer == null) return;

        // Create a transition with custom settings
        var transition = asset.CreateTransition(customFadeTime);
        
        // Play the transition
        var state = animancer.Play(transition);
        
        Debug.Log($"[AnimancerMagicBlendExample] Playing custom MagicBlend transition: {asset.name} with fade time: {customFadeTime}");
    }

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Animancer + MagicBlend Example", GUI.skin.box);
        
        GUILayout.Label("Movement:");
        GUILayout.Label("W - Walk, Shift+W - Run");
        
        GUILayout.Label("Actions:");
        GUILayout.Label("Right Click - Aim, R - Reload, H - Injured");
        
        GUILayout.Space(10);
        
        if (GUILayout.Button($"Base Pose Mode: {(useAnimatedBasePose ? "Animated" : "Static")}"))
        {
            ToggleBasePoseMode();
        }
        
        GUILayout.Space(10);
        GUILayout.Label($"Current State: {(animancer?.States.Current?.ToString() ?? "None")}");
        
        GUILayout.EndArea();
    }
}
