// #if UNITY_EDITOR
// using System.IO;
// using Animancer;
// using NewAnimancer;
// using UnityEditor;
// using UnityEngine;
//
// //
//  public static class MixerJsonTools
//  {
//      // ────────────────────────── 1. <PERSON><PERSON>PORT ──────────────────────────
//      [CustomEditor(typeof(MixerTransition2DAsset), true)]
//      [CanEditMultipleObjects]
//      class Legacy2DEditor : UnityEditor.Editor
//      {
//          public override void OnInspectorGUI()
//          {
//              DrawDefaultInspector();
//              if (GUILayout.Button("Export JSON"))  Export(target);
//          }
//      }
//      
//      [CustomEditor(typeof(LinearMixerTransitionAsset), true)]
//      [CanEditMultipleObjects]
//      class LegacyLinearEditor : UnityEditor.Editor
//      {
//          public override void OnInspectorGUI()
//          {
//              DrawDefaultInspector();
//              if (GUILayout.But<PERSON>("Export JSON"))  Export(target);
//          }
//      }
//      
//      static void Export(Object asset)
//      {
//          var path = AssetDatabase.GetAssetPath(asset);
//          var jsonPath = Path.ChangeExtension(path, "json");
//          File.WriteAllText(jsonPath, EditorJsonUtility.ToJson(asset, true));
//          AssetDatabase.Refresh();
//          Debug.Log($"Exported → {jsonPath}", asset);
//      }
//
//     // ────────────────────────── 2. IMPORT ──────────────────────────
//     const string ImportMenu = "Assets/Animancer/Import Mixer JSON → TransitionAsset";
//
//     [MenuItem(ImportMenu, validate = true)]
//     static bool CanImport() => Selection.activeObject &&
//                                Path.GetExtension(AssetDatabase.GetAssetPath(Selection.activeObject)) == ".json";
//
//     [MenuItem(ImportMenu)]
//     static void Import()
//     {
//         var jsonPath = AssetDatabase.GetAssetPath(Selection.activeObject);
//         var json = File.ReadAllText(jsonPath);
//
//         // Swap legacy namespace → new namespace so types resolve.
//         json = json.Replace("\"Animancer.", "\"NewAnimancer.");
//
//         // Wrap JSON in a dummy ScriptableObject to keep references intact.
//         var wrapper = ScriptableObject.CreateInstance<TransitionAsset>();
//         EditorJsonUtility.FromJsonOverwrite(json, wrapper);
//
//         var outPath = Path.Combine(Path.GetDirectoryName(jsonPath),
//                                    Path.GetFileNameWithoutExtension(jsonPath) + "_New.asset");
//         outPath = AssetDatabase.GenerateUniqueAssetPath(outPath);
//
//         AssetDatabase.CreateAsset(wrapper, outPath);
//         EditorUtility.SetDirty(wrapper);
//         AssetDatabase.SaveAssets();
//
//         Debug.Log($"Imported JSON → {outPath}", wrapper);
//         Selection.activeObject = wrapper;
//     }
// }
// #endif