%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 16
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2d2426b1932e491d8bfde743167c5274, type: 3}
  m_Name: Animancer Settings 2
  m_EditorClassIdentifier: 
  _Data:
  - rid: 1809481991901676059
  - rid: 1809481991901676060
  - rid: 1809481991901676061
  - rid: 1809481991901676062
  - rid: 1809481991901676063
  - rid: 1809481991901676064
  references:
    version: 2
    RefIds:
    - rid: 1809481991901676059
      type: {class: AnimancerComponentPreviewSettings, ns: NewAnimancer.Editor.Previews,
        asm: Kybernetik.Animancer.Editor}
      data:
        _RepaintRate: 30
    - rid: 1809481991901676060
      type: {class: AnimancerGraphControls, ns: NewAnimancer.Editor, asm: Kybernetik.Animancer.Editor}
      data:
        _FrameStep: 0.02
    - rid: 1809481991901676061
      type: {class: TransitionPreviewSettings, ns: NewAnimancer.Editor.Previews, asm: Kybernetik.Animancer.Editor}
      data:
        _AutoClose: 1
        _SceneLighting: 0
        _ShowSkybox: 0
        _FrameStep: 0.02
        _SceneEnvironment: {fileID: 0}
        _Models: []
    - rid: 1809481991901676062
      type: {class: SerializableEventSequenceDrawerSettings, ns: NewAnimancer.Editor,
        asm: Kybernetik.Animancer.Editor}
      data:
        _HideEventCallbacks: 0
    - rid: 1809481991901676063
      type: {class: AnimationTimeAttributeSettings, ns: NewAnimancer.Units.Editor,
        asm: Kybernetik.Animancer.Editor}
      data:
        showApproximations: 1
        showNormalized: 1
        showSeconds: 1
        showFrames: 1
    - rid: 1809481991901676064
      type: {class: GenerateSpriteAnimationsSettings, ns: NewAnimancer.Editor.Tools,
        asm: Kybernetik.Animancer.Editor}
      data:
        _FrameRate: 12
        _HierarchyPath: 
        _TargetType:
          _QualifiedName: UnityEngine.SpriteRenderer, UnityEngine.CoreModule, Version=0.0.0.0,
            Culture=neutral, PublicKeyToken=null
        _PropertyName: m_Sprite
