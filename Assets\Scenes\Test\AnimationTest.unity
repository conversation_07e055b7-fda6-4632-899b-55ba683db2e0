%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 20201, guid: 0000000000000000f000000000000000, type: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &5386602
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5386603}
  - component: {fileID: 5386604}
  m_Layer: 0
  m_Name: CC_Base_Tongue
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5386603
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5386602}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &5386604
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5386602}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: e053034ff17f24c4786eea07a8c73342, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 1654403436102926152, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 1347422854}
  - {fileID: 1699357223}
  - {fileID: 1926856646}
  m_BlendShapeWeights:
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  m_RootBone: {fileID: 1926856646}
  m_AABB:
    m_Center: {x: -0.03286753, y: 0.003165951, z: -0.00006536394}
    m_Extent: {x: 0.054333396, y: 0.029733827, z: 0.025414914}
  m_DirtyAABB: 0
--- !u!1 &46562586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 46562587}
  m_Layer: 6
  m_Name: ForwardAimTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &46562587
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 46562586}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.049999952, y: 1.09, z: 2.04}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 350000798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &95360379
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 95360380}
  m_Layer: 0
  m_Name: ball_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &95360380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 95360379}
  serializedVersion: 2
  m_LocalRotation: {x: 0.4711623, y: -0.020159509, z: 0.024802616, w: 0.8814673}
  m_LocalPosition: {x: -0.0000018896537, y: 0.14396675, z: 0.00000074505806}
  m_LocalScale: {x: 1, y: 0.9999998, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1065482879}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &122185266
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 122185267}
  - component: {fileID: 122185268}
  m_Layer: 6
  m_Name: AimIK After Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &122185267
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122185266}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 350000798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &122185268
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122185266}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 1.234939, y: -1.4151461, z: 0.32664528}
    IKPositionWeight: 1
    root: {fileID: 122185267}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 6
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 791390003}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 3.352761e-10, y: 0.038068235, z: 0.00000034332274}
      defaultLocalRotation: {x: -0.12483239, y: 0.000005946884, z: 0.00025184738,
        w: 0.99217784}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    - transform: {fileID: 2036937282}
      weight: 0.721
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: -7.8231094e-10, y: 0.12466316, z: 0.0000017356872}
      defaultLocalRotation: {x: -0.15933207, y: -0.00021606042, z: -0.0018017701,
        w: 0.9872234}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    - transform: {fileID: 2084455373}
      weight: 0.433
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.0000000077486035, y: 0.26940826, z: -0.0000020599364}
      defaultLocalRotation: {x: 0.26952836, y: -0.0010340491, z: 0.0076741115, w: 0.9629613}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 788945576}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: -0.8694594, y: 1.5595894, z: 2.499767}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!1 &123809141
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 123809142}
  m_Layer: 0
  m_Name: ring_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &123809142
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 123809141}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0017240138, y: 0.019061547, z: 0.3346126, w: -0.9421614}
  m_LocalPosition: {x: -0.0000015766129, y: 0.027640875, z: -0.0000021375859}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1331996526}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &129380940
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 129380941}
  m_Layer: 0
  m_Name: thumb_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &129380941
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 129380940}
  serializedVersion: 2
  m_LocalRotation: {x: 0.21791503, y: 0.01546745, z: -0.0679087, w: -0.9734795}
  m_LocalPosition: {x: -0.0000007273631, y: 0.05436759, z: -0.00000074878346}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1253068708}
  m_Father: {fileID: 439328758}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &130749903
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 130749904}
  m_Layer: 0
  m_Name: index_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &130749904
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 130749903}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0128645245, y: 0.01292789, z: 0.3180652, w: 0.94789344}
  m_LocalPosition: {x: -0.0004373443, y: 0.028221816, z: -0.0007229042}
  m_LocalScale: {x: 1.0000004, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 479989158}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &203844586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 203844589}
  - component: {fileID: 203844588}
  - component: {fileID: 203844587}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &203844587
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 203844586}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!108 &203844588
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 203844586}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &203844589
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 203844586}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!1 &221709057
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 221709058}
  m_Layer: 0
  m_Name: CC_Base_UpperJaw
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &221709058
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221709057}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00005004184, y: 0.0000016683813, z: 1, w: 0.000009986938}
  m_LocalPosition: {x: 0.057560336, y: 0.018850708, z: 0.000037461232}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 640277526}
  m_Father: {fileID: 1194285621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &221890808
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 221890809}
  m_Layer: 0
  m_Name: lowerarm_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &221890809
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221890808}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000020659559, y: -0.0000015906267, z: 0.00000052282104,
    w: 1}
  m_LocalPosition: {x: -0, y: 0.12450798, z: 0.00000007152557}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1313544555}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &233545466
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 233545467}
  m_Layer: 0
  m_Name: CC_Base_L_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &233545467
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 233545466}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999708, y: 0.5000029, z: 0.49999705, w: 0.5000029}
  m_LocalPosition: {x: 0.06261507, y: 0.05849472, z: 0.031659685}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1194285621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &236533410
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 236533411}
  - component: {fileID: 236533413}
  - component: {fileID: 236533412}
  m_Layer: 6
  m_Name: Aim Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &236533411
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236533410}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.7583194, y: 1.09, z: 13.26319}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &236533412
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236533410}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: aae886dd0d5d59844b4ec40cc2d96918, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &236533413
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 236533410}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &268200737
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 268200738}
  m_Layer: 0
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &268200738
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 268200737}
  serializedVersion: 2
  m_LocalRotation: {x: -0.86622465, y: 0.018038174, z: -0.010375827, w: 0.49922118}
  m_LocalPosition: {x: 0.0030536088, y: 0.22314286, z: 0.11494481}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 500869698}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &289717371
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 289717372}
  m_Layer: 0
  m_Name: pinky_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &289717372
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 289717371}
  serializedVersion: 2
  m_LocalRotation: {x: -0.007037891, y: 0.027214263, z: 0.3123169, w: -0.9495621}
  m_LocalPosition: {x: 0.00000015855768, y: 0.033094056, z: -0.00000008738882}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 532689012}
  m_Father: {fileID: 2071019250}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &302967735
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 302967736}
  - component: {fileID: 302967738}
  - component: {fileID: 302967737}
  m_Layer: 21
  m_Name: Laser2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &302967736
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 302967735}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000007, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1162545655}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!23 &302967737
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 302967735}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &302967738
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 302967735}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &350000797
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 350000798}
  m_Layer: 6
  m_Name: AimHelper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &350000798
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 350000797}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1185579696}
  - {fileID: 122185267}
  - {fileID: 896993602}
  - {fileID: 46562587}
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &365752385
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 365752386}
  m_Layer: 0
  m_Name: spine_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &365752386
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 365752385}
  serializedVersion: 2
  m_LocalRotation: {x: 0.16194536, y: 0.000021845102, z: -0.00013518333, w: 0.9867998}
  m_LocalPosition: {x: 0.000016985054, y: 0.07044195, z: 0.018796988}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 791390003}
  m_Father: {fileID: 788567744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &391831347
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 391831348}
  m_Layer: 0
  m_Name: clavicle_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &391831348
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391831347}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0058371844, y: -0.17127489, z: 0.6711395, w: 0.72125083}
  m_LocalPosition: {x: -0.046346955, y: 0.21288395, z: 0.0042236745}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 922988970}
  m_Father: {fileID: 2036937282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &393862046
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 393862047}
  m_Layer: 0
  m_Name: hand_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &393862047
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 393862046}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10251306, y: -0.0019429382, z: 0.026160479, w: 0.99438566}
  m_LocalPosition: {x: 0.00000012721283, y: 0.24901822, z: 0.000000119209275}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 493927424}
  - {fileID: 507680816}
  - {fileID: 2071019250}
  - {fileID: 2037831840}
  - {fileID: 439328758}
  - {fileID: 1074454516}
  m_Father: {fileID: 1313544555}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &402353772
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 402353773}
  m_Layer: 0
  m_Name: CC_Base_JawRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &402353773
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 402353772}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000026639605, y: -0.00006712115, z: 0.995976, w: 0.08962046}
  m_LocalPosition: {x: 0.026865957, y: 0.011196997, z: -0.00015041608}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 596914987}
  - {fileID: 1926856646}
  m_Father: {fileID: 1194285621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &418392750
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 418392751}
  - component: {fileID: 418392752}
  m_Layer: 0
  m_Name: Middle_Ponytail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &418392751
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 418392750}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &418392752
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 418392750}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 2d91b221636cd93469c2cff02bb387f9, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 1155122597573229543, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 791390003}
  - {fileID: 2036937282}
  - {fileID: 2084455373}
  - {fileID: 2117149252}
  - {fileID: 391831348}
  - {fileID: 818439002}
  - {fileID: 652127906}
  - {fileID: 2132946929}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 791390003}
  m_AABB:
    m_Center: {x: -0.0010192692, y: 0.46571648, z: -0.122491054}
    m_Extent: {x: 0.091920495, y: 0.17998058, z: 0.1596455}
  m_DirtyAABB: 0
--- !u!1 &426692019
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 426692020}
  - component: {fileID: 426692021}
  m_Layer: 0
  m_Name: Ines_Body_V1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &426692020
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 426692019}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &426692021
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 426692019}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8de11d2c34af9584e98ad8fe854167f8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4083910945696752190, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 1654331587}
  - {fileID: 1285479708}
  - {fileID: 1348073460}
  - {fileID: 1007695412}
  - {fileID: 1065482879}
  - {fileID: 500869698}
  - {fileID: 922988970}
  - {fileID: 1143128288}
  - {fileID: 2036937282}
  - {fileID: 391831348}
  - {fileID: 1729486767}
  - {fileID: 821742122}
  - {fileID: 2132946929}
  - {fileID: 659195098}
  - {fileID: 526572840}
  - {fileID: 788567744}
  - {fileID: 1427020990}
  - {fileID: 1862972380}
  - {fileID: 1639236270}
  - {fileID: 1313544555}
  - {fileID: 2084455373}
  - {fileID: 2117149252}
  - {fileID: 402353773}
  - {fileID: 818439002}
  - {fileID: 791390003}
  - {fileID: 652127906}
  - {fileID: 365752386}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 788567744}
  m_AABB:
    m_Center: {x: 0.0009224564, y: -0.20033973, z: 0.015904918}
    m_Extent: {x: 0.43948817, y: 0.78844875, z: 0.21551071}
  m_DirtyAABB: 0
--- !u!1 &439328757
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 439328758}
  m_Layer: 0
  m_Name: thumb_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &439328758
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 439328757}
  serializedVersion: 2
  m_LocalRotation: {x: -0.40827867, y: 0.17583132, z: 0.21280256, w: -0.8701189}
  m_LocalPosition: {x: 0.008959771, y: 0.02282992, z: 0.018451856}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 129380941}
  m_Father: {fileID: 393862047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &441880493
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 441880494}
  m_Layer: 0
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &441880494
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 441880493}
  serializedVersion: 2
  m_LocalRotation: {x: -0.86621135, y: -0.017392404, z: 0.010017169, w: 0.4992747}
  m_LocalPosition: {x: -0.0029380997, y: 0.2230568, z: 0.11497639}
  m_LocalScale: {x: 1.0000001, y: 0.99999976, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1065482879}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &479989157
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 479989158}
  m_Layer: 0
  m_Name: index_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &479989158
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 479989157}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000056894438, y: 0.012458745, z: 0.31955138, w: 0.94748706}
  m_LocalPosition: {x: 0.0000002079178, y: 0.03848414, z: 0.00000030916544}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 130749904}
  m_Father: {fileID: 2095647995}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &490280330
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 490280331}
  m_Layer: 0
  m_Name: lowerarm_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &490280331
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 490280330}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000020261016, y: -0.0000023856562, z: -0.0000005890616,
    w: 1}
  m_LocalPosition: {x: 0.0000016784668, y: 0.12341133, z: -0.0000041913986}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1639236270}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &493927423
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 493927424}
  m_Layer: 0
  m_Name: index_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &493927424
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 493927423}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0062812725, y: 0.04405264, z: 0.32261693, w: -0.9454831}
  m_LocalPosition: {x: 0.0016203529, y: 0.09508161, z: 0.017892258}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1030914214}
  m_Father: {fileID: 393862047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &500869697
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 500869698}
  m_Layer: 0
  m_Name: foot_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &500869698
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 500869697}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26213837, y: -0.07327941, z: 0.035943866, w: 0.9615725}
  m_LocalPosition: {x: -0.00000042747706, y: 0.49376294, z: -0.0000005960465}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1522317842}
  - {fileID: 268200738}
  m_Father: {fileID: 1348073460}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &507680815
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 507680816}
  m_Layer: 0
  m_Name: middle_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &507680816
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 507680815}
  serializedVersion: 2
  m_LocalRotation: {x: 0.03435428, y: 0.042340882, z: 0.3233633, w: -0.94470274}
  m_LocalPosition: {x: -0.00000030826786, y: 0.09293064, z: 0.000000016647395}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 709975675}
  m_Father: {fileID: 393862047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &510648139
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 510648140}
  m_Layer: 0
  m_Name: middle_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &510648140
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 510648139}
  serializedVersion: 2
  m_LocalRotation: {x: 0.012394886, y: 0.016453868, z: 0.31939703, w: 0.94739705}
  m_LocalPosition: {x: 0.0000005824259, y: 0.044478916, z: -0.00000023060784}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1823403080}
  m_Father: {fileID: 729983694}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &526572839
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 526572840}
  m_Layer: 0
  m_Name: thigh_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &526572840
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 526572839}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0057723834, y: 0.25908446, z: 0.9656182, w: -0.020581087}
  m_LocalPosition: {x: -0.09741045, y: -0.021480039, z: -0.002206655}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1654331587}
  - {fileID: 659195098}
  m_Father: {fileID: 788567744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &532689011
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 532689012}
  m_Layer: 0
  m_Name: pinky_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &532689012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 532689011}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0055833636, y: 0.025886443, z: 0.35945466, w: -0.93278676}
  m_LocalPosition: {x: -0.00000011170053, y: 0.0161742, z: -0.00000006224037}
  m_LocalScale: {x: 0.9999999, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 289717372}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &557736755
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 557736756}
  - component: {fileID: 557736757}
  m_Layer: 0
  m_Name: CC_Base_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &557736756
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 557736755}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &557736757
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 557736755}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4372044fa75068540a70e02fce99869d, type: 2}
  - {fileID: 2100000, guid: 9eb706bdb64514d40aebd42dff2b7421, type: 2}
  - {fileID: 2100000, guid: a74d138b42e43c34aa7d8acec14b25f8, type: 2}
  - {fileID: 2100000, guid: e4995a92ac6380b4aa7bd9e587bdcee4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7654024864961597166, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 233545467}
  - {fileID: 2131469343}
  m_BlendShapeWeights:
  - 0
  - 0
  m_RootBone: {fileID: 2131469343}
  m_AABB:
    m_Center: {x: -0.031884596, y: -0.00096217636, z: -0.00013517216}
    m_Extent: {x: 0.048445284, y: 0.01696821, z: 0.01673663}
  m_DirtyAABB: 0
--- !u!1 &596914986
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 596914987}
  m_Layer: 0
  m_Name: CC_Base_Teeth02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &596914987
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 596914986}
  serializedVersion: 2
  m_LocalRotation: {x: 1, y: 0.00022650788, z: -0.00009729427, w: -0.00008053859}
  m_LocalPosition: {x: -0.024823641, y: 0.012739562, z: 0.00014736093}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 402353773}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &596950363
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 596950364}
  m_Layer: 0
  m_Name: thumb_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &596950364
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 596950363}
  serializedVersion: 2
  m_LocalRotation: {x: -0.23369977, y: 0.0043788254, z: -0.0803165, w: 0.968976}
  m_LocalPosition: {x: 0.000000045634813, y: 0.026586972, z: 0.00000019371512}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1486610823}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &640277525
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 640277526}
  m_Layer: 0
  m_Name: CC_Base_Teeth01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &640277526
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 640277525}
  serializedVersion: 2
  m_LocalRotation: {x: 1, y: 0.00023426232, z: 0.00003416539, w: 0.000011044115}
  m_LocalPosition: {x: -0.0010065723, y: 0.0037287902, z: -0.000025710386}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 221709058}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &652127905
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 652127906}
  m_Layer: 0
  m_Name: CC_Base_R_RibsTwist
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &652127906
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 652127905}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0010908963, y: 0.6153887, z: 0.78822196, w: -0.0013186043}
  m_LocalPosition: {x: 0.09821229, y: 0.047898404, z: 0.14247963}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2036937282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &659195097
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 659195098}
  m_Layer: 0
  m_Name: thigh_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &659195098
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 659195097}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000008643836, y: 0.0000018727438, z: -6.0897634e-21, w: 1}
  m_LocalPosition: {x: -0.0000002813339, y: 0.23965019, z: -0.00000030994414}
  m_LocalScale: {x: 1.0000002, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 526572840}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &709975674
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 709975675}
  m_Layer: 0
  m_Name: middle_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &709975675
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 709975674}
  serializedVersion: 2
  m_LocalRotation: {x: -0.012596294, y: 0.020259678, z: 0.3189513, w: -0.9474709}
  m_LocalPosition: {x: -0.00000056589494, y: 0.044478264, z: -0.00000031473613}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 873247521}
  m_Father: {fileID: 507680816}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &729983693
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 729983694}
  m_Layer: 0
  m_Name: middle_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &729983694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 729983693}
  serializedVersion: 2
  m_LocalRotation: {x: -0.035471946, y: 0.039232872, z: 0.32390818, w: 0.944609}
  m_LocalPosition: {x: -0.000000054016724, y: 0.09293024, z: 0.00000003667083}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 510648140}
  m_Father: {fileID: 1107498345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &788567743
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 788567744}
  m_Layer: 0
  m_Name: pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &788567744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 788567743}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071064, y: 0.000010992124, z: -0.000016333257, w: 0.70710725}
  m_LocalPosition: {x: -0.0017271759, y: 0.013507819, z: -0.02622914}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 365752386}
  - {fileID: 526572840}
  - {fileID: 1862972380}
  m_Father: {fileID: 1751654721}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &788945575
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 788945576}
  m_Layer: 21
  m_Name: AimTransform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &788945576
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 788945575}
  serializedVersion: 2
  m_LocalRotation: {x: 0.4837934, y: 0.51108474, z: -0.4920204, w: 0.51249623}
  m_LocalPosition: {x: -0.025851786, y: 0.00839515, z: 0.0016169167}
  m_LocalScale: {x: 1.0152817, y: 1.0099486, z: 0.9751789}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1162545655}
  m_Father: {fileID: 1074454516}
  m_LocalEulerAnglesHint: {x: -108.337, y: -265.796, z: 272.1}
--- !u!1 &791390002
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 791390003}
  m_Layer: 0
  m_Name: spine_02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &791390003
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 791390002}
  serializedVersion: 2
  m_LocalRotation: {x: -0.12483237, y: 0.0000059753665, z: 0.0002518296, w: 0.9921778}
  m_LocalPosition: {x: 3.2196112e-10, y: 0.038068287, z: 0.00000032875678}
  m_LocalScale: {x: 1.0000001, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2036937282}
  m_Father: {fileID: 365752386}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &794541316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 794541317}
  m_Layer: 0
  m_Name: pinky_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &794541317
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 794541316}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0059519103, y: 0.022700317, z: 0.35952085, w: 0.93284196}
  m_LocalPosition: {x: 0.00000034226107, y: 0.016170712, z: -0.00000029638983}
  m_LocalScale: {x: 1.0000002, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1777047158}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &816158918
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 816158919}
  - component: {fileID: 816158920}
  m_Layer: 0
  m_Name: Ines_Boots_V1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &816158919
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 816158918}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &816158920
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 816158918}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a2bce4765e81d974bb67c6b0b904ff40, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 2213655293627143353, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 1065482879}
  - {fileID: 1285479708}
  - {fileID: 1654331587}
  - {fileID: 500869698}
  - {fileID: 1007695412}
  - {fileID: 1348073460}
  - {fileID: 95360380}
  - {fileID: 1522317842}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1348073460}
  m_AABB:
    m_Center: {x: 0.07914308, y: 0.54150337, z: 0.0098627955}
    m_Extent: {x: 0.1488527, y: 0.14436929, z: 0.14782879}
  m_DirtyAABB: 0
--- !u!1 &818439001
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 818439002}
  m_Layer: 0
  m_Name: CC_Base_L_RibsTwist
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &818439002
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 818439001}
  serializedVersion: 2
  m_LocalRotation: {x: -0.001101011, y: 0.6153887, z: 0.7882219, w: -0.0013287836}
  m_LocalPosition: {x: -0.098425545, y: 0.047770996, z: 0.14246082}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2036937282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &821742121
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 821742122}
  m_Layer: 0
  m_Name: upperarm_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &821742122
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 821742121}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000019607223, y: -0.00000022029232, z: 0.0000012521631,
    w: 1}
  m_LocalPosition: {x: 0.00000045776366, y: 0.14070854, z: 0.0000006866455}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1729486767}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &873247520
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 873247521}
  m_Layer: 0
  m_Name: middle_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &873247521
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 873247520}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0054703737, y: 0.025574114, z: 0.33307493, w: -0.94253767}
  m_LocalPosition: {x: -0.000027199632, y: 0.028993504, z: 0.000005135887}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 709975675}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &896993601
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 896993602}
  - component: {fileID: 896993603}
  m_Layer: 6
  m_Name: LimbIK Left Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &896993602
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896993601}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 350000798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &896993603
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 896993601}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4db3c450680fd4c809d5ad90a2f24e5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: -0.67545736, y: 1.582675, z: -0.06562546}
    IKPositionWeight: 0
    root: {fileID: 896993602}
    target: {fileID: 0}
    IKRotationWeight: 0
    IKRotation: {x: 0.06974718, y: 0.07264444, z: 0.68527913, w: 0.72128403}
    bendNormal: {x: -0.00000012719387, y: -0.000000040853752, z: -0.0006869835}
    bone1:
      transform: {fileID: 922988970}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: 0.00000015258789, y: 0.10289588, z: 0.0000000047683715}
      defaultLocalRotation: {x: 0.12092967, y: 0.0028766154, z: 0.029973214, w: 0.9922043}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone2:
      transform: {fileID: 1639236270}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0.00000061035155, y: 0.2813307, z: -0.000000009536743}
      defaultLocalRotation: {x: -0.00000048121444, y: -0.000012135147, z: -0.004907392,
        w: 0.99998796}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone3:
      transform: {fileID: 1107498345}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: 0.00000015258789, y: 0.24876311, z: -0.000000038146972}
      defaultLocalRotation: {x: 0.10059828, y: 0.001801374, z: -0.0280344, w: 0.99453044}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    goal: 2
    bendModifier: 0
    maintainRotationWeight: 0
    bendModifierWeight: 1
    bendGoal: {fileID: 0}
--- !u!1 &922988969
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 922988970}
  m_Layer: 0
  m_Name: upperarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &922988970
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 922988969}
  serializedVersion: 2
  m_LocalRotation: {x: 0.35353935, y: -0.033741195, z: 0.35527602, w: 0.8646678}
  m_LocalPosition: {x: 0.00000013085084, y: 0.102896124, z: 0.00000006216578}
  m_LocalScale: {x: 0.99999976, y: 0.9999998, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1639236270}
  - {fileID: 1143128288}
  m_Father: {fileID: 391831348}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &961739749
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 961739753}
  - component: {fileID: 961739752}
  - component: {fileID: 961739751}
  - component: {fileID: 961739750}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &961739750
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 961739749}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: -1
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
--- !u!81 &961739751
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 961739749}
  m_Enabled: 1
--- !u!20 &961739752
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 961739749}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &961739753
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 961739749}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1, z: -10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1007695411
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1007695412}
  m_Layer: 0
  m_Name: calf_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1007695412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1007695411}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000016143078, y: 0.0001604101, z: 0.000012576289, w: 1}
  m_LocalPosition: {x: -0.00000021934508, y: 0.24688132, z: -0.0000002670288}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1348073460}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1011972849
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1074454516}
    m_Modifications:
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 1.0152811
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.9751786
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 1.0099491
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.13600597
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.020960063
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.0021262132
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.70930266
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.01348043
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70448285
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.020296047
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6508003453768678230, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_Name
      value: AimPointer
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2c513e3cb59a72543b45a4c740779f56, type: 3}
--- !u!4 &1011972850 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
    type: 3}
  m_PrefabInstance: {fileID: 1011972849}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1030914213
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1030914214}
  m_Layer: 0
  m_Name: index_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1030914214
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1030914213}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0001571877, y: 0.016267497, z: 0.31922346, w: -0.9475398}
  m_LocalPosition: {x: -0.00000020908195, y: 0.038483683, z: 0.00000021420698}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1823914913}
  m_Father: {fileID: 493927424}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1036107749
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1036107750}
  - component: {fileID: 1036107751}
  m_Layer: 0
  m_Name: CC_Base_Teeth
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1036107750
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036107749}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1036107751
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1036107749}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: abfb25e4f694953438ae403c6e9d6024, type: 2}
  - {fileID: 2100000, guid: 6fa55045d8ab1c147a17e89a9c56285d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -5727283436533813174, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 596914987}
  - {fileID: 640277526}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 640277526}
  m_AABB:
    m_Center: {x: -0.001902123, y: -0.018704455, z: 0.000000009313226}
    m_Extent: {x: 0.024316978, y: 0.01927523, z: 0.023962753}
  m_DirtyAABB: 0
--- !u!1 &1038214040
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1038214041}
  - component: {fileID: 1038214042}
  m_Layer: 9
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1038214041
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1038214040}
  serializedVersion: 2
  m_LocalRotation: {x: -0.86621135, y: -0.017392404, z: 0.010017169, w: 0.4992747}
  m_LocalPosition: {x: -0.0029380997, y: 0.2230568, z: 0.11497639}
  m_LocalScale: {x: 1.0000001, y: 0.99999976, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2057176480679072977}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1038214042
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1038214040}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.12159323, y: 0.32100618, z: 0.12159323}
  m_Center: {x: -0.0031298567, y: 0.17521249, z: -0.045183767}
--- !u!1 &1056498518
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1056498519}
  - component: {fileID: 1056498520}
  m_Layer: 0
  m_Name: ToKo_Underwear_Bra_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1056498519
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1056498518}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1056498520
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1056498518}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d013a4ba5c8aaa2458cdb9a205cd76fb, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7698357121477753718, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 652127906}
  - {fileID: 2036937282}
  - {fileID: 791390003}
  - {fileID: 818439002}
  - {fileID: 391831348}
  - {fileID: 2132946929}
  - {fileID: 922988970}
  - {fileID: 1729486767}
  - {fileID: 2084455373}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 791390003}
  m_AABB:
    m_Center: {x: -0.00050093234, y: 0.26455414, z: -0.025684223}
    m_Extent: {x: 0.16582833, y: 0.18954834, z: 0.17397897}
  m_DirtyAABB: 0
--- !u!1 &1065482878
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1065482879}
  m_Layer: 0
  m_Name: foot_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1065482879
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1065482878}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26242968, y: 0.07238963, z: -0.036033694, w: 0.96155715}
  m_LocalPosition: {x: 0.00000045076, y: 0.49370182, z: -0.00000068545336}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 95360380}
  - {fileID: 441880494}
  m_Father: {fileID: 1654331587}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1074454515
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 393862047}
    m_Modifications:
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_RootOrder
      value: 17
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.9897865
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 1.0257374
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.9850558
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.125
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.177
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.032
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.55991447
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.5254256
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.576247
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.27993417
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 15.412
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 257.036
      objectReference: {fileID: 0}
    - target: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -72.423
      objectReference: {fileID: 0}
    - target: {fileID: 6360584711328263943, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      propertyPath: m_Name
      value: SciFi_Pistol_4
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 788945576}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1950600341}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1011972850}
    - targetCorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 2062405671}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2c900257853a7a74f9b11e31d554c873, type: 3}
--- !u!4 &1074454516 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5984869065698588093, guid: 2c900257853a7a74f9b11e31d554c873,
    type: 3}
  m_PrefabInstance: {fileID: 1074454515}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1107498344
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1107498345}
  m_Layer: 0
  m_Name: hand_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1107498345
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1107498344}
  serializedVersion: 2
  m_LocalRotation: {x: 0.100598276, y: 0.0018013357, z: -0.028034348, w: 0.9945305}
  m_LocalPosition: {x: 0.00000021723096, y: 0.24876517, z: -0.000000044703476}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2095647995}
  - {fileID: 729983694}
  - {fileID: 1775810478}
  - {fileID: 1708922341}
  - {fileID: 1770285347}
  m_Father: {fileID: 1639236270}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1143128287
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1143128288}
  m_Layer: 0
  m_Name: upperarm_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1143128288
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1143128287}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000017129787, y: 0.00000023047708, z: -0.0000014104878,
    w: 1}
  m_LocalPosition: {x: -0.00000030517577, y: 0.14066535, z: -0.0000000047683715}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 922988970}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1162545654
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1162545655}
  m_Layer: 21
  m_Name: Aim
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1162545655
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1162545654}
  serializedVersion: 2
  m_LocalRotation: {x: -0.703968, y: -0.7074759, z: -0.040998396, w: 0.04718122}
  m_LocalPosition: {x: -0.016999971, y: 0.0030000117, z: 0.007999996}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1370722055}
  - {fileID: 302967736}
  m_Father: {fileID: 788945576}
  m_LocalEulerAnglesHint: {x: -172.852, y: 0.5220032, z: -90.31702}
--- !u!1 &1185579695
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1185579696}
  - component: {fileID: 1185579698}
  - component: {fileID: 1185579697}
  m_Layer: 6
  m_Name: AimIK Before Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1185579696
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1185579695}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 350000798}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1185579697
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1185579695}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: Only bones that used as muscle targets by PuppetMaster should ba added to
    AimIK's "Bones".
--- !u!114 &1185579698
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1185579695}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 1.234939, y: -1.4151461, z: 0.32664528}
    IKPositionWeight: 0
    root: {fileID: 1185579696}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 4
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 365752386}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.000016985006, y: 0.07044197, z: 0.018796988}
      defaultLocalRotation: {x: 0.16194537, y: 0.000021887443, z: -0.00013517268,
        w: 0.9867998}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 788945576}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: -0.8694594, y: 1.5595894, z: 2.499767}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!1 &1194285620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1194285621}
  m_Layer: 0
  m_Name: CC_Base_FacialBone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1194285621
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1194285620}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000017471123, y: -0.70710677, z: -0.0000017471124, w: 0.70710677}
  m_LocalPosition: {x: 0.0000000026177787, y: 0.0000003576279, z: 0.000000026078295}
  m_LocalScale: {x: 1, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 402353773}
  - {fileID: 233545467}
  - {fileID: 2131469343}
  - {fileID: 221709058}
  m_Father: {fileID: 2117149252}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1253068707
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1253068708}
  m_Layer: 0
  m_Name: thumb_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1253068708
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1253068707}
  serializedVersion: 2
  m_LocalRotation: {x: 0.23376386, y: 0.0045397724, z: -0.08019013, w: -0.9689703}
  m_LocalPosition: {x: -0.00000011175872, y: 0.026587524, z: 0.00000024400651}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 129380941}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1285479707
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1285479708}
  m_Layer: 0
  m_Name: calf_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1285479708
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1285479707}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000035669185, y: -0.000019150324, z: -0.000027639298, w: 1}
  m_LocalPosition: {x: 0.00000021934508, y: 0.24685065, z: -0.00000035762787}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1654331587}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1313544554
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1313544555}
  m_Layer: 0
  m_Name: lowerarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1313544555
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1313544554}
  serializedVersion: 2
  m_LocalRotation: {x: 0.64271826, y: 0.005757572, z: 0.005592018, w: 0.76606065}
  m_LocalPosition: {x: 0.0000007450582, y: 0.2814179, z: 0.00000092387234}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 393862047}
  - {fileID: 221890809}
  m_Father: {fileID: 1729486767}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1314009586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1314009587}
  - component: {fileID: 1314009588}
  m_Layer: 0
  m_Name: CC_Base_Body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1314009587
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1314009586}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1314009588
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1314009586}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 231f293b4d8f36f46bf37186c629de1e, type: 2}
  - {fileID: 2100000, guid: ed2b5a2049d359c4bbf89fd1847dddb0, type: 2}
  - {fileID: 2100000, guid: 2ad698a8dad45fc4a8d37c31b58dcdf5, type: 2}
  - {fileID: 2100000, guid: ee7c162b70ff995438710e2235472f8c, type: 2}
  - {fileID: 2100000, guid: 7cdfc14f5abeb8d4d93f1cc553af6919, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 2988033852023935963, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 1639236270}
  - {fileID: 490280331}
  - {fileID: 788567744}
  - {fileID: 791390003}
  - {fileID: 365752386}
  - {fileID: 2036937282}
  - {fileID: 2117149252}
  - {fileID: 402353773}
  - {fileID: 2084455373}
  - {fileID: 526572840}
  - {fileID: 1862972380}
  - {fileID: 1143128288}
  - {fileID: 922988970}
  - {fileID: 1654331587}
  - {fileID: 1285479708}
  - {fileID: 1065482879}
  - {fileID: 659195098}
  - {fileID: 1107498345}
  - {fileID: 729983694}
  - {fileID: 510648140}
  - {fileID: 2095647995}
  - {fileID: 1823403080}
  - {fileID: 1708922341}
  - {fileID: 479989158}
  - {fileID: 130749904}
  - {fileID: 1657561067}
  - {fileID: 1681152083}
  - {fileID: 1775810478}
  - {fileID: 1777047158}
  - {fileID: 794541317}
  - {fileID: 1770285347}
  - {fileID: 1486610823}
  - {fileID: 596950364}
  - {fileID: 95360380}
  - {fileID: 391831348}
  - {fileID: 818439002}
  - {fileID: 652127906}
  - {fileID: 2132946929}
  - {fileID: 1313544555}
  - {fileID: 221890809}
  - {fileID: 821742122}
  - {fileID: 1729486767}
  - {fileID: 1348073460}
  - {fileID: 1007695412}
  - {fileID: 500869698}
  - {fileID: 1427020990}
  - {fileID: 507680816}
  - {fileID: 709975675}
  - {fileID: 393862047}
  - {fileID: 493927424}
  - {fileID: 873247521}
  - {fileID: 2037831840}
  - {fileID: 1030914214}
  - {fileID: 1823914913}
  - {fileID: 1331996526}
  - {fileID: 123809142}
  - {fileID: 2071019250}
  - {fileID: 289717372}
  - {fileID: 532689012}
  - {fileID: 439328758}
  - {fileID: 129380941}
  - {fileID: 1253068708}
  - {fileID: 1522317842}
  m_BlendShapeWeights:
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  m_RootBone: {fileID: 788567744}
  m_AABB:
    m_Center: {x: 0.00039353967, y: 0.5694388, z: 0.004483696}
    m_Extent: {x: 0.8668691, y: 0.162474, z: 0.102908105}
  m_DirtyAABB: 0
--- !u!1 &1331996525
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1331996526}
  m_Layer: 0
  m_Name: ring_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1331996526
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1331996525}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0028674062, y: 0.020136401, z: 0.31277582, w: -0.9496092}
  m_LocalPosition: {x: 0.00000019930307, y: 0.042684704, z: -0.000000009495128}
  m_LocalScale: {x: 1.0000001, y: 1.0000004, z: 1.0000004}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 123809142}
  m_Father: {fileID: 2037831840}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1347422853
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1347422854}
  m_Layer: 0
  m_Name: CC_Base_Tongue02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1347422854
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1347422853}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0011598348, y: -0.005633796, z: -0.09942047, w: 0.9950289}
  m_LocalPosition: {x: -0.009785385, y: 0.0002241516, z: -0.0000043535233}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1699357223}
  m_Father: {fileID: 1926856646}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1348073459
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1348073460}
  m_Layer: 0
  m_Name: calf_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1348073460
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1348073459}
  serializedVersion: 2
  m_LocalRotation: {x: -0.6465417, y: -0.0132559575, z: 0.018137911, w: 0.76254785}
  m_LocalPosition: {x: 0.00000021420419, y: 0.47928715, z: -0.00000023841855}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1007695412}
  - {fileID: 500869698}
  m_Father: {fileID: 1862972380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1370722054
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1370722055}
  - component: {fileID: 1370722057}
  - component: {fileID: 1370722056}
  m_Layer: 21
  m_Name: Laser1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1370722055
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370722054}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000002, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1162545655}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!23 &1370722056
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370722054}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1370722057
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370722054}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1427020989
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1427020990}
  m_Layer: 0
  m_Name: thigh_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1427020990
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1427020989}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000012267265, y: -0.0000017183896, z: 0.00000011269007,
    w: 1}
  m_LocalPosition: {x: 0.00000012397766, y: 0.2396434, z: -0.00000011920929}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1862972380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1486610822
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1486610823}
  m_Layer: 0
  m_Name: thumb_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1486610823
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1486610822}
  serializedVersion: 2
  m_LocalRotation: {x: -0.21795404, y: 0.0152288675, z: -0.06782591, w: 0.9734802}
  m_LocalPosition: {x: 0.0000007729979, y: 0.054367814, z: -0.0000007720665}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 596950364}
  m_Father: {fileID: 1770285347}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1522317841
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1522317842}
  m_Layer: 0
  m_Name: ball_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1522317842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1522317841}
  serializedVersion: 2
  m_LocalRotation: {x: 0.4711399, y: 0.02005827, z: -0.02584058, w: 0.8814517}
  m_LocalPosition: {x: 0.000004895031, y: 0.14396676, z: 0.000001594424}
  m_LocalScale: {x: 0.9999997, y: 0.99999964, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 500869698}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1603960883
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1603960884}
  - component: {fileID: 1603960890}
  - component: {fileID: 1603960889}
  - component: {fileID: 1603960886}
  - component: {fileID: 1603960892}
  - component: {fileID: 1603960891}
  - component: {fileID: 1603960894}
  m_Layer: 8
  m_Name: Female1 (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1603960884
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1314009587}
  - {fileID: 557736756}
  - {fileID: 1036107750}
  - {fileID: 5386603}
  - {fileID: 426692020}
  - {fileID: 816158919}
  - {fileID: 1634441398}
  - {fileID: 418392751}
  - {fileID: 1751654721}
  - {fileID: 2059762510}
  - {fileID: 1056498519}
  - {fileID: 350000798}
  m_Father: {fileID: 2060465284}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1603960886
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d6ad7b53f86f9da4da426b673c422513, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _Animator: {fileID: 1603960890}
  _Transitions: {fileID: 0}
  _ActionOnDisable: 0
  _PlayAutomatically: 0
  _Animations: []
  _Controller:
    _FadeDuration: 0.25
    _Speed: 1
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Controller: {fileID: 9100000, guid: 063544d9180ba394b9e7c497c8372a7d, type: 2}
    _ParameterBindings:
      _Mode: 0
      _Bindings: []
    _ActionsOnStop: 
  references:
    version: 2
    RefIds: []
--- !u!114 &1603960889
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cdf51b00bdc44c699c58a3ed985775d4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  blendAsset: {fileID: 11400000, guid: 6fcc1e3faff94884af6bac841671ac98, type: 2}
  externalWeight: 1
  forceUpdateWeights: 1
  alwaysAnimatePoses: 1
  useAnimancerAsBaseInput: 0
--- !u!95 &1603960890
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Controller: {fileID: 0}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &1603960891
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ab8cfa865e8923b45b97f2e6e5d21ce9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _Animancer: {fileID: 0}
  _ActionMask: {fileID: 31900000, guid: 3b2eadacc632a3740a676d32bf591776, type: 2}
  _ActionFadeDuration: 0.25
  initializeMagicBlend: 1
  magicBlendComponent: {fileID: 1603960889}
  enableMagicBlendBoneRebinding: 1
--- !u!114 &1603960892
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0816b76efdaf0f8499691c5103211fc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _AnimationManager: {fileID: 0}
  m_currentUpperBodyAnimation: {fileID: 11400000, guid: 6fcc1e3faff94884af6bac841671ac98,
    type: 2}
  _originalRigRoot: {fileID: 1751654721}
  _clothingRigRoot: {fileID: 0}
  useMagicBlendIntegration: 1
  defaultBlendTime: 0.25
--- !u!114 &1603960894
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603960883}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bde7c7d170fd4189bbdba0e6cc7da8a9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  animancer: {fileID: 1603960886}
  targetAsset: {fileID: 11400000, guid: 52514418f25e4b63addd064fb61beaaa, type: 2}
  targetTransition: {fileID: 11400000, guid: 405558cf36c3bd541ac850a46458fd7a, type: 2}
  basePoseOptions:
  - {fileID: 7400000, guid: 1610c7939855ed245b50b03f44aab19e, type: 2}
  - {fileID: 7400046, guid: e7468f78a01b86e439c96f57154dab25, type: 3}
  overlayPoseOptions:
  - {fileID: 7400094, guid: e7468f78a01b86e439c96f57154dab25, type: 3}
  useRuntimeCopy: 1
  newBlendTime: 0.25
  newGlobalWeight: 1
--- !u!1 &1634441397
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1634441398}
  - component: {fileID: 1634441399}
  m_Layer: 0
  m_Name: Ines_Default_Gloves
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1634441398
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1634441397}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1634441399
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1634441397}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 622c22f36d9b9c94d89d6f00a899b550, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 279771734379647476, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 1708922341}
  - {fileID: 1657561067}
  - {fileID: 1107498345}
  - {fileID: 1775810478}
  - {fileID: 1777047158}
  - {fileID: 729983694}
  - {fileID: 490280331}
  - {fileID: 2095647995}
  - {fileID: 1770285347}
  - {fileID: 1639236270}
  - {fileID: 1486610823}
  - {fileID: 479989158}
  - {fileID: 510648140}
  - {fileID: 2037831840}
  - {fileID: 1331996526}
  - {fileID: 393862047}
  - {fileID: 2071019250}
  - {fileID: 289717372}
  - {fileID: 507680816}
  - {fileID: 221890809}
  - {fileID: 493927424}
  - {fileID: 439328758}
  - {fileID: 1313544555}
  - {fileID: 129380941}
  - {fileID: 1030914214}
  - {fileID: 709975675}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1313544555}
  m_AABB:
    m_Center: {x: 0.0060192235, y: -0.42701393, z: 0.025629144}
    m_Extent: {x: 0.041894034, y: 0.81338686, z: 0.07448232}
  m_DirtyAABB: 0
--- !u!1 &1639236269
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1639236270}
  m_Layer: 0
  m_Name: lowerarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1639236270
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1639236269}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6427098, y: -0.006489644, z: -0.0037715663, w: 0.76607305}
  m_LocalPosition: {x: -0.0000005960465, y: 0.2813315, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1107498345}
  - {fileID: 490280331}
  m_Father: {fileID: 922988970}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1654331586
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1654331587}
  m_Layer: 0
  m_Name: calf_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1654331587
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1654331586}
  serializedVersion: 2
  m_LocalRotation: {x: -0.646798, y: 0.013238879, z: -0.016659563, w: 0.7623645}
  m_LocalPosition: {x: -0.00000054202985, y: 0.47930062, z: -0.0000006407499}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1285479708}
  - {fileID: 1065482879}
  m_Father: {fileID: 526572840}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1657561066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1657561067}
  m_Layer: 0
  m_Name: ring_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1657561067
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1657561066}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0012214995, y: 0.016848808, z: 0.31292173, w: 0.9496287}
  m_LocalPosition: {x: 0.00000055145944, y: 0.041719615, z: -0.00000071620445}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1681152083}
  m_Father: {fileID: 1708922341}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1659082871
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1659082872}
  - component: {fileID: 1659082873}
  m_Layer: 0
  m_Name: Behaviours
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1659082872
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1659082871}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2060465284}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1659082873
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1659082871}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: All Puppet Behaviours should be parented to this GameObject, the PuppetMaster
    will automatically find them from here. All Puppet Behaviours have been designed
    so that they could be simply copied from one character to another without changing
    any references. It is important because they contain a lot of parameters and
    would be otherwise tedious to set up and tweak.
--- !u!1 &1681152082
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1681152083}
  m_Layer: 0
  m_Name: ring_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1681152083
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1681152082}
  serializedVersion: 2
  m_LocalRotation: {x: 0.001411002, y: 0.017358664, z: 0.33464137, w: 0.9421846}
  m_LocalPosition: {x: -0.00000051769905, y: 0.026329383, z: 0.000000035463703}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1657561067}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1699357222
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1699357223}
  m_Layer: 0
  m_Name: CC_Base_Tongue03
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1699357223
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1699357222}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000046749043, y: -0.0010550104, z: 0.11461715, w: 0.9934092}
  m_LocalPosition: {x: -0.013736267, y: 0.000017242432, z: 0.00000092945993}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1347422854}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1708922340
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1708922341}
  m_Layer: 0
  m_Name: ring_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1708922341
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1708922340}
  serializedVersion: 2
  m_LocalRotation: {x: -0.109960675, y: 0.03511855, z: 0.3183628, w: 0.94091475}
  m_LocalPosition: {x: -0.0015324334, y: 0.088897504, z: -0.01653782}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1657561067}
  m_Father: {fileID: 1107498345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1729486766
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1729486767}
  m_Layer: 0
  m_Name: upperarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1729486767
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1729486766}
  serializedVersion: 2
  m_LocalRotation: {x: 0.35020316, y: 0.03190345, z: -0.35329235, w: 0.8669052}
  m_LocalPosition: {x: -0.00000013830142, y: 0.100599766, z: -0.00000043236645}
  m_LocalScale: {x: 0.99999976, y: 0.99999964, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1313544555}
  - {fileID: 821742122}
  m_Father: {fileID: 2132946929}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1751654720
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1751654721}
  - component: {fileID: 1751654722}
  m_Layer: 0
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1751654721
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1751654720}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0.0000029843027, y: 0, z: -0.03379729}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 788567744}
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1751654722
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1751654720}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e03e7ed42ce9470c899fc6cc550571e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: _hierarchyMap
      Entry: 6
      Data: 
  hierarchy:
  - {fileID: 1751654721}
  - {fileID: 788567744}
  - {fileID: 365752386}
  - {fileID: 791390003}
  - {fileID: 2036937282}
  - {fileID: 818439002}
  - {fileID: 652127906}
  - {fileID: 391831348}
  - {fileID: 922988970}
  - {fileID: 1639236270}
  - {fileID: 1107498345}
  - {fileID: 2095647995}
  - {fileID: 479989158}
  - {fileID: 130749904}
  - {fileID: 729983694}
  - {fileID: 510648140}
  - {fileID: 1823403080}
  - {fileID: 1775810478}
  - {fileID: 1777047158}
  - {fileID: 794541317}
  - {fileID: 1708922341}
  - {fileID: 1657561067}
  - {fileID: 1681152083}
  - {fileID: 1770285347}
  - {fileID: 1486610823}
  - {fileID: 596950364}
  - {fileID: 490280331}
  - {fileID: 1143128288}
  - {fileID: 2132946929}
  - {fileID: 1729486767}
  - {fileID: 1313544555}
  - {fileID: 393862047}
  - {fileID: 493927424}
  - {fileID: 1030914214}
  - {fileID: 1823914913}
  - {fileID: 507680816}
  - {fileID: 709975675}
  - {fileID: 873247521}
  - {fileID: 2071019250}
  - {fileID: 289717372}
  - {fileID: 532689012}
  - {fileID: 2037831840}
  - {fileID: 1331996526}
  - {fileID: 123809142}
  - {fileID: 439328758}
  - {fileID: 129380941}
  - {fileID: 1253068708}
  - {fileID: 221890809}
  - {fileID: 821742122}
  - {fileID: 2084455373}
  - {fileID: 2117149252}
  - {fileID: 1194285621}
  - {fileID: 402353773}
  - {fileID: 596914987}
  - {fileID: 1926856646}
  - {fileID: 1347422854}
  - {fileID: 1699357223}
  - {fileID: 233545467}
  - {fileID: 2131469343}
  - {fileID: 221709058}
  - {fileID: 640277526}
  - {fileID: 526572840}
  - {fileID: 1654331587}
  - {fileID: 1285479708}
  - {fileID: 1065482879}
  - {fileID: 95360380}
  - {fileID: 659195098}
  - {fileID: 1862972380}
  - {fileID: 1348073460}
  - {fileID: 1007695412}
  - {fileID: 500869698}
  - {fileID: 1522317842}
  - {fileID: 1427020990}
  _virtualElements: []
  _cachedHierarchyPose: []
  hierarchyDepths: 0000000001000000020000000300000004000000050000000500000005000000060000000700000008000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b000000080000000700000005000000060000000700000008000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b000000090000000a0000000b00000008000000070000000500000006000000070000000800000009000000090000000a0000000b00000008000000080000000800000009000000020000000300000004000000040000000500000003000000020000000300000004000000040000000500000003000000
--- !u!1 &1770285346
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1770285347}
  m_Layer: 0
  m_Name: thumb_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1770285347
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1770285346}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40757298, y: 0.17505525, z: 0.21308547, w: 0.87053686}
  m_LocalPosition: {x: -0.008979087, y: 0.022824226, z: 0.018420167}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1486610823}
  m_Father: {fileID: 1107498345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1775810477
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1775810478}
  m_Layer: 0
  m_Name: pinky_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1775810478
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1775810477}
  serializedVersion: 2
  m_LocalRotation: {x: -0.14139149, y: 0.038567513, z: 0.3272725, w: 0.93349546}
  m_LocalPosition: {x: -0.005909313, y: 0.082680196, z: -0.030114096}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1777047158}
  m_Father: {fileID: 1107498345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1777047157
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1777047158}
  m_Layer: 0
  m_Name: pinky_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1777047158
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1777047157}
  serializedVersion: 2
  m_LocalRotation: {x: 0.006781398, y: 0.024002517, z: 0.31257668, w: 0.949565}
  m_LocalPosition: {x: 0.0000003366732, y: 0.033087667, z: -0.000000014747458}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 794541317}
  m_Father: {fileID: 1775810478}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1823403079
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1823403080}
  m_Layer: 0
  m_Name: middle_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1823403080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1823403079}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0057915617, y: 0.02177615, z: 0.33323914, w: 0.942573}
  m_LocalPosition: {x: 0.00003727654, y: 0.029057303, z: 0.000018892706}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 510648140}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1823914912
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1823914913}
  m_Layer: 0
  m_Name: index_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1823914913
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1823914912}
  serializedVersion: 2
  m_LocalRotation: {x: 0.01261167, y: 0.016738364, z: 0.31796965, w: -0.9478693}
  m_LocalPosition: {x: 0.00045138545, y: 0.028107531, z: -0.0007394696}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1030914214}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1862972379
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1862972380}
  m_Layer: 0
  m_Name: thigh_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1862972380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1862972379}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0060504675, y: 0.25895494, z: 0.96563125, w: 0.021496192}
  m_LocalPosition: {x: 0.0974114, y: -0.021429222, z: -0.0022249052}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1348073460}
  - {fileID: 1427020990}
  m_Father: {fileID: 788567744}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1863590578
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1863590579}
  - component: {fileID: 1863590580}
  m_Layer: 9
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1863590579
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1863590578}
  serializedVersion: 2
  m_LocalRotation: {x: -0.86622465, y: 0.018038174, z: -0.010375827, w: 0.49922118}
  m_LocalPosition: {x: 0.0030536088, y: 0.22314286, z: 0.11494481}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7342576458949274615}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1863590580
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1863590578}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.12159393, y: 0.32100797, z: 0.12159393}
  m_Center: {x: 0.0032451823, y: 0.17523569, z: -0.045210827}
--- !u!1 &1926856645
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1926856646}
  m_Layer: 0
  m_Name: CC_Base_Tongue01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1926856646
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1926856645}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00029571867, y: 0.006775168, z: -0.11056743, w: 0.9938455}
  m_LocalPosition: {x: -0.02315749, y: 0.011044769, z: -0.00015940386}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1347422854}
  m_Father: {fileID: 402353773}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1949235395
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1949235397}
  - component: {fileID: 1949235396}
  m_Layer: 0
  m_Name: DebugLogManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1949235396
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1949235395}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1cbb37e6a1ce5e24f8a5180c05b1e779, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _settings: {fileID: 11400000, guid: d1b9c644629aebc499b9107aeb5c6ce1, type: 2}
--- !u!4 &1949235397
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1949235395}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1950600340
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1950600341}
  m_Layer: 21
  m_Name: Pointer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1950600341
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1950600340}
  serializedVersion: 2
  m_LocalRotation: {x: 0.77128386, y: -0.026550846, z: 0.0323993, w: -0.6351116}
  m_LocalPosition: {x: -0.13594225, y: -0.02086684, z: 0.004009595}
  m_LocalScale: {x: 1.010579, y: 1.0150763, z: 0.9747495}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1074454516}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2036937281
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2036937282}
  m_Layer: 0
  m_Name: spine_03
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2036937282
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2036937281}
  serializedVersion: 2
  m_LocalRotation: {x: -0.15933202, y: -0.00021612641, z: -0.0018017887, w: 0.9872234}
  m_LocalPosition: {x: -7.858033e-10, y: 0.12466317, z: 0.0000017378482}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 818439002}
  - {fileID: 652127906}
  - {fileID: 391831348}
  - {fileID: 2132946929}
  - {fileID: 2084455373}
  m_Father: {fileID: 791390003}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2037831839
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2037831840}
  m_Layer: 0
  m_Name: ring_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2037831840
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2037831839}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10743107, y: 0.03920835, z: 0.32272127, w: -0.93955964}
  m_LocalPosition: {x: 0.0020114228, y: 0.08760652, z: -0.015832042}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1331996526}
  m_Father: {fileID: 393862047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2059762509
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2059762510}
  - component: {fileID: 2059762511}
  m_Layer: 0
  m_Name: Sci_Fi_BGraph
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2059762510
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2059762509}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1603960884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &2059762511
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2059762509}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 75b47edf762049149a922ff6504fef34, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7392520633234987534, guid: 24f165a1c59b4a1448e9e6006543ee75, type: 3}
  m_Bones:
  - {fileID: 2036937282}
  - {fileID: 1729486767}
  - {fileID: 2132946929}
  - {fileID: 652127906}
  - {fileID: 391831348}
  - {fileID: 922988970}
  - {fileID: 2084455373}
  - {fileID: 791390003}
  - {fileID: 818439002}
  - {fileID: 490280331}
  - {fileID: 1639236270}
  - {fileID: 1107498345}
  - {fileID: 221890809}
  - {fileID: 1313544555}
  - {fileID: 393862047}
  - {fileID: 1143128288}
  - {fileID: 821742122}
  - {fileID: 365752386}
  - {fileID: 788567744}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 788567744}
  m_AABB:
    m_Center: {x: 0.0010021627, y: 0.30867955, z: 0.021864533}
    m_Extent: {x: 0.6461469, y: 0.24538127, z: 0.20483333}
  m_DirtyAABB: 0
--- !u!1 &2060465283
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2060465284}
  m_Layer: 0
  m_Name: Female1 (1) Root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2060465284
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2060465283}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1659082872}
  - {fileID: 2718649208880983646}
  - {fileID: 1603960884}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2062405670
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2062405671}
  m_Layer: 21
  m_Name: Left Hand Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2062405671
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2062405670}
  serializedVersion: 2
  m_LocalRotation: {x: 0.25672767, y: -0.010788244, z: 0.79459333, w: -0.5500874}
  m_LocalPosition: {x: 0.028688993, y: -0.09583068, z: -0.004698406}
  m_LocalScale: {x: 0.9793076, y: 1.0065103, z: 1.0144148}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1074454516}
  m_LocalEulerAnglesHint: {x: 55.4101, y: -238.4923, z: -34.3354}
--- !u!1 &2071019249
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2071019250}
  m_Layer: 0
  m_Name: pinky_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2071019250
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2071019249}
  serializedVersion: 2
  m_LocalRotation: {x: 0.14113404, y: 0.041423872, z: 0.3272475, w: -0.93342084}
  m_LocalPosition: {x: 0.0059110494, y: 0.08264578, z: -0.030138062}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 289717372}
  m_Father: {fileID: 393862047}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2084455372
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2084455373}
  m_Layer: 0
  m_Name: neck_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2084455373
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2084455372}
  serializedVersion: 2
  m_LocalRotation: {x: 0.2695284, y: -0.0010341108, z: 0.007674157, w: 0.9629614}
  m_LocalPosition: {x: 0.000000007683412, y: 0.26940826, z: -0.000002078712}
  m_LocalScale: {x: 0.9999999, y: 1.0000001, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2117149252}
  m_Father: {fileID: 2036937282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2095647994
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2095647995}
  m_Layer: 0
  m_Name: index_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2095647995
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2095647994}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0051638926, y: 0.04095576, z: 0.32314914, w: 0.9454473}
  m_LocalPosition: {x: -0.001621192, y: 0.09508102, z: 0.017892344}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 479989158}
  m_Father: {fileID: 1107498345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2117149251
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2117149252}
  m_Layer: 0
  m_Name: head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2117149252
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2117149251}
  serializedVersion: 2
  m_LocalRotation: {x: -0.14982721, y: 0.0007031407, z: -0.0061363867, w: 0.9886929}
  m_LocalPosition: {x: -0.00000002421439, y: 0.07173049, z: 0.000000044703473}
  m_LocalScale: {x: 1.0000001, y: 0.9999996, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1194285621}
  m_Father: {fileID: 2084455373}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2131469342
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2131469343}
  m_Layer: 0
  m_Name: CC_Base_R_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2131469343
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2131469342}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5000003, y: -0.49999973, z: -0.5000003, w: -0.49999976}
  m_LocalPosition: {x: 0.06211906, y: 0.058755912, z: -0.032109868}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1194285621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2132946928
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2132946929}
  m_Layer: 0
  m_Name: clavicle_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2132946929
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2132946928}
  serializedVersion: 2
  m_LocalRotation: {x: 0.009416448, y: 0.16666275, z: -0.6722113, w: 0.72129524}
  m_LocalPosition: {x: 0.047479425, y: 0.21393648, z: 0.002574429}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1729486767}
  m_Father: {fileID: 2036937282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &20886035627276422
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8507390164161050080}
  - component: {fileID: 8507390164161050082}
  - component: {fileID: 8507390164161050081}
  - component: {fileID: 8507390164161050083}
  m_Layer: 9
  m_Name: lowerarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &242333729163533167
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5127213141868635989}
  - component: {fileID: 5127213141868635991}
  - component: {fileID: 5127213141868635990}
  - component: {fileID: 5127213141868635992}
  m_Layer: 9
  m_Name: calf_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &391968514503198763
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6359360710549211175}
  serializedVersion: 2
  m_LocalRotation: {x: -0.026243053, y: 0.026773466, z: 0.71181405, w: 0.7013668}
  m_LocalPosition: {x: -0.14536102, y: 0.33108318, z: -0.08794619}
  m_LocalScale: {x: 0.99999976, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4163107321374676012}
  m_Father: {fileID: 3672910278980562935}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &739689946495207884
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3656840819856038287}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00025808235, y: 0.00028837987, z: 0.99977154, w: -0.021374444}
  m_LocalPosition: {x: -0.09741045, y: -0.021480039, z: -0.00220668}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5127213141868635989}
  m_Father: {fileID: 4334577748699632903}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &931668667661383315
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5931440703684339416}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10059828, y: 0.001801374, z: -0.0280344, w: 0.99453044}
  m_LocalPosition: {x: -0.00000004121102, y: 0.24876305, z: -0.00000003562698}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4163107321374676012}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1081494905603953626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4334577748699632903}
  - component: {fileID: 4334577748699632905}
  - component: {fileID: 4334577748699632904}
  - component: {fileID: 4334577748699632906}
  m_Layer: 9
  m_Name: pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1161014949539902938
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3366497561474141229}
  serializedVersion: 2
  m_LocalRotation: {x: -0.005145862, y: -0.00012410844, z: 0.02421719, w: 0.99969345}
  m_LocalPosition: {x: 0.00000020675361, y: 0.47928712, z: -0.00000013661337}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7342576458949274615}
  m_Father: {fileID: 1802066833543739433}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1372511727054042636
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4452510089258981396}
  - component: {fileID: 4452510089258981399}
  - component: {fileID: 4452510089258981398}
  - component: {fileID: 4452510089258981397}
  m_Layer: 9
  m_Name: head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1802066833543739433
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3965297141383911434}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000269411, y: 0.00016035745, z: 0.9997506, w: 0.022329211}
  m_LocalPosition: {x: 0.0974114, y: -0.021429218, z: -0.0022248626}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1161014949539902938}
  m_Father: {fileID: 4334577748699632903}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &2057176480679072977
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2378520955643520241}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26242954, y: 0.072389625, z: -0.036033686, w: 0.96155715}
  m_LocalPosition: {x: 0.00000044517208, y: 0.49370146, z: -0.00000059790904}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1038214041}
  m_Father: {fileID: 5127213141868635989}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2378520955643520241
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2057176480679072977}
  - component: {fileID: 2378520955643520242}
  - component: {fileID: 2378520955643520243}
  m_Layer: 9
  m_Name: foot_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!54 &2378520955643520242
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2378520955643520241}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &2378520955643520243
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2378520955643520241}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5127213141868635991}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &2718649208880983646
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3382561355986842852}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4334577748699632903}
  m_Father: {fileID: 2060465284}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3366497561474141229
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1161014949539902938}
  - component: {fileID: 3366497561474141231}
  - component: {fileID: 3366497561474141230}
  - component: {fileID: 3366497561474141232}
  m_Layer: 9
  m_Name: calf_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &3366497561474141230
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3366497561474141229}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.097055644
  m_Height: 0.543139
  m_Direction: 1
  m_Center: {x: -0.00000021420419, y: 0.24688114, z: -0.00000020023438}
--- !u!54 &3366497561474141231
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3366497561474141229}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &3366497561474141232
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3366497561474141229}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 3965297141383911436}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -2.837453
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 137.16255
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &3382561355986842852
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2718649208880983646}
  - component: {fileID: 3382561355986842853}
  m_Layer: 9
  m_Name: PuppetMaster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &3382561355986842853
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3382561355986842852}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1c86ba130e5a5458a98e3b482192a6dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  humanoidConfig: {fileID: 0}
  targetRoot: {fileID: 1603960884}
  state: 0
  stateSettings:
    killDuration: 1
    deadMuscleWeight: 0.01
    deadMuscleDamper: 2
    maxFreezeSqrVelocity: 0.02
    freezePermanently: 0
    enableAngularLimitsOnKill: 1
    enableInternalCollisionsOnKill: 1
  mode: 0
  blendTime: 0.1
  fixTargetTransforms: 1
  solverIterationCount: 6
  visualizeTargetPose: 1
  mappingWeight: 1
  pinWeight: 1
  muscleWeight: 1
  muscleSpring: 100
  muscleDamper: 0
  pinPow: 4
  pinDistanceFalloff: 5
  angularPinning: 0
  updateJointAnchors: 1
  supportTranslationAnimation: 0
  angularLimits: 0
  internalCollisions: 0
  muscles:
  - name: pelvis
    joint: {fileID: 4334577748699632906}
    target: {fileID: 788567744}
    props:
      group: 0
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 0
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: thigh_l
    joint: {fileID: 3656840819856038290}
    target: {fileID: 526572840}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 1
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: calf_l
    joint: {fileID: 5127213141868635992}
    target: {fileID: 1654331587}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 2
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: foot_l
    joint: {fileID: 2378520955643520243}
    target: {fileID: 1065482879}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 3
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: thigh_r
    joint: {fileID: 3965297141383911437}
    target: {fileID: 1862972380}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 4
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: calf_r
    joint: {fileID: 3366497561474141232}
    target: {fileID: 1348073460}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 5
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: foot_r
    joint: {fileID: 7342576458949274617}
    target: {fileID: 500869698}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 6
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: spine_02
    joint: {fileID: 5016455502951505493}
    target: {fileID: 791390003}
    props:
      group: 1
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 7
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: upperarm_l
    joint: {fileID: 6359360710549211178}
    target: {fileID: 922988970}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 8
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: lowerarm_l
    joint: {fileID: 4229797488933211704}
    target: {fileID: 1639236270}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 9
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: hand_l
    joint: {fileID: 5931440703684339419}
    target: {fileID: 1107498345}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 10
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: upperarm_r
    joint: {fileID: 8215425578896893514}
    target: {fileID: 1729486767}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 11
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: lowerarm_r
    joint: {fileID: 8507390164161050083}
    target: {fileID: 1313544555}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 12
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: hand_r
    joint: {fileID: 9031080444131072388}
    target: {fileID: 393862047}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 13
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: head
    joint: {fileID: 4452510089258981397}
    target: {fileID: 2117149252}
    props:
      group: 2
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 14
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  propMuscles: []
  solvers: []
  mapDisconnectedMuscles: 1
  storeTargetMappedState: 1
--- !u!1 &3656840819856038287
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 739689946495207884}
  - component: {fileID: 3656840819856038289}
  - component: {fileID: 3656840819856038288}
  - component: {fileID: 3656840819856038290}
  m_Layer: 9
  m_Name: thigh_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &3656840819856038288
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3656840819856038287}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.10784255
  m_Height: 0.52723026
  m_Direction: 1
  m_Center: {x: -0.0000002859161, y: 0.23964992, z: -0.00000034327965}
--- !u!54 &3656840819856038289
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3656840819856038287}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &3656840819856038290
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3656840819856038287}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 4334577748699632905}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &3672910278980562935
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5016455502951505490}
  serializedVersion: 2
  m_LocalRotation: {x: 0.037494026, y: 0.00000367303, z: 0.00011810292, w: 0.9992969}
  m_LocalPosition: {x: 0.000027410932, y: 0.106513344, z: 0.030964514}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 391968514503198763}
  - {fileID: 8215425578896893511}
  - {fileID: 4452510089258981396}
  m_Father: {fileID: 4334577748699632903}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3965297141383911434
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1802066833543739433}
  - component: {fileID: 3965297141383911436}
  - component: {fileID: 3965297141383911435}
  - component: {fileID: 3965297141383911437}
  m_Layer: 9
  m_Name: thigh_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &3965297141383911435
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3965297141383911434}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.107839614
  m_Height: 0.52721584
  m_Direction: 1
  m_Center: {x: 0.00000011175871, y: 0.23964334, z: -0.00000005801848}
--- !u!54 &3965297141383911436
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3965297141383911434}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &3965297141383911437
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3965297141383911434}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 4334577748699632905}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &4163107321374676012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4229797488933211701}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000048987545, y: -0.000012127677, z: -0.004907473, w: 0.99998796}
  m_LocalPosition: {x: -0.0000004894101, y: 0.28133073, z: -0.00000010879015}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 931668667661383315}
  m_Father: {fileID: 391968514503198763}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4229797488933211701
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4163107321374676012}
  - component: {fileID: 4229797488933211703}
  - component: {fileID: 4229797488933211702}
  - component: {fileID: 4229797488933211704}
  m_Layer: 9
  m_Name: lowerarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &4229797488933211702
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4229797488933211701}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.075959265
  m_Height: 0.2736391
  m_Direction: 1
  m_Center: {x: 0.0000005689216, y: 0.12438155, z: -0.00000009420073}
--- !u!54 &4229797488933211703
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4229797488933211701}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &4229797488933211704
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4229797488933211701}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 6359360710549211177}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -0.5623221
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 139.43768
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &4334577748699632903
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1081494905603953626}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000006556511, y: -0.0000038331455, z: -0.00001936654, w: 1}
  m_LocalPosition: {x: -0.0000029843027, y: 1.1449528, z: -0.03379741}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 739689946495207884}
  - {fileID: 1802066833543739433}
  - {fileID: 3672910278980562935}
  m_Father: {fileID: 2718649208880983646}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &4334577748699632904
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1081494905603953626}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.23321414, y: 0.17533009, z: 0.13992849}
  m_Center: {x: 0.0000044474327, y: 0.028164964, z: 0.016374627}
--- !u!54 &4334577748699632905
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1081494905603953626}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &4334577748699632906
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1081494905603953626}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 0}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 2
  m_YMotion: 2
  m_ZMotion: 2
  m_AngularXMotion: 2
  m_AngularYMotion: 2
  m_AngularZMotion: 2
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &4452510089258981396
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1372511727054042636}
  serializedVersion: 2
  m_LocalRotation: {x: -0.037495185, y: -0.000004858333, z: -0.00009620549, w: 0.99929684}
  m_LocalPosition: {x: 0.00010460752, y: 0.45029494, z: -0.0686966}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3672910278980562935}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!153 &4452510089258981397
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1372511727054042636}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5016455502951505491}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!136 &4452510089258981398
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1372511727054042636}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.14914049
  m_Height: 0.27342424
  m_Direction: 1
  m_Center: {x: 0.00015779176, y: 0.0654333, z: -0.014051051}
--- !u!54 &4452510089258981399
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1372511727054042636}
  serializedVersion: 5
  m_Mass: 5.4900002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &5016455502951505490
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3672910278980562935}
  - component: {fileID: 5016455502951505491}
  - component: {fileID: 5016455502951505492}
  - component: {fileID: 5016455502951505493}
  m_Layer: 9
  m_Name: spine_02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!54 &5016455502951505491
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5016455502951505490}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &5016455502951505492
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5016455502951505490}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.29151765, y: 0.43816063, z: 0.17491059}
  m_Center: {x: 0.00012554858, y: 0.19527864, z: -0.039149012}
--- !u!153 &5016455502951505493
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5016455502951505490}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 4334577748699632905}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &5028939843267514978
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8215425578896893511}
  - component: {fileID: 8215425578896893513}
  - component: {fileID: 8215425578896893512}
  - component: {fileID: 8215425578896893514}
  m_Layer: 9
  m_Name: upperarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5127213141868635989
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 242333729163533167}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00548152, y: 0.00012282522, z: -0.022256006, w: 0.99973726}
  m_LocalPosition: {x: -0.00000054202985, y: 0.4793005, z: -0.0000004945031}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2057176480679072977}
  m_Father: {fileID: 739689946495207884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &5127213141868635990
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 242333729163533167}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.09705828
  m_Height: 0.54307145
  m_Direction: 1
  m_Center: {x: 0.0000002207234, y: 0.24685049, z: -0.00000030291264}
--- !u!54 &5127213141868635991
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 242333729163533167}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &5127213141868635992
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 242333729163533167}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 3656840819856038289}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -2.62689
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 137.37311
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &5931440703684339416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 931668667661383315}
  - component: {fileID: 5931440703684339418}
  - component: {fileID: 5931440703684339417}
  - component: {fileID: 5931440703684339419}
  m_Layer: 9
  m_Name: hand_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &5931440703684339417
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5931440703684339416}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.069964565
  m_Height: 0.20522939
  m_Direction: 1
  m_Center: {x: 0.00000040698797, y: 0.093286045, z: -0.000000031664964}
--- !u!54 &5931440703684339418
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5931440703684339416}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &5931440703684339419
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5931440703684339416}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 4229797488933211703}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &6172228005745030767
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9031080444131072385}
  - component: {fileID: 9031080444131072387}
  - component: {fileID: 9031080444131072386}
  - component: {fileID: 9031080444131072388}
  m_Layer: 9
  m_Name: hand_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &6359360710549211175
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 391968514503198763}
  - component: {fileID: 6359360710549211177}
  - component: {fileID: 6359360710549211176}
  - component: {fileID: 6359360710549211178}
  m_Layer: 9
  m_Name: upperarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!136 &6359360710549211176
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6359360710549211175}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08439919
  m_Height: 0.30946368
  m_Direction: 1
  m_Center: {x: 0.0000002495945, y: 0.14066541, z: -0.0000000111758744}
--- !u!54 &6359360710549211177
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6359360710549211175}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &6359360710549211178
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6359360710549211175}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5016455502951505491}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &7212030796461448557
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7342576458949274615}
  - component: {fileID: 7342576458949274616}
  - component: {fileID: 7342576458949274617}
  m_Layer: 9
  m_Name: foot_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7342576458949274615
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7212030796461448557}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26213843, y: -0.07327937, z: 0.035943985, w: 0.9615725}
  m_LocalPosition: {x: -0.0000004246831, y: 0.49376267, z: -0.00000063888734}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1863590579}
  m_Father: {fileID: 1161014949539902938}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &7342576458949274616
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7212030796461448557}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &7342576458949274617
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7212030796461448557}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 3366497561474141231}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &8215425578896893511
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5028939843267514978}
  serializedVersion: 2
  m_LocalRotation: {x: -0.02410228, y: -0.028923262, z: -0.7123166, w: 0.7008478}
  m_LocalPosition: {x: 0.14615616, y: 0.33055305, z: -0.08785249}
  m_LocalScale: {x: 0.99999976, y: 0.9999995, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8507390164161050080}
  m_Father: {fileID: 3672910278980562935}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &8215425578896893512
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5028939843267514978}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08442511
  m_Height: 0.30955872
  m_Direction: 1
  m_Center: {x: -0.00000006286429, y: 0.14070858, z: 0.00000039115562}
--- !u!54 &8215425578896893513
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5028939843267514978}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &8215425578896893514
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5028939843267514978}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5016455502951505491}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &8507390164161050080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 20886035627276422}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000016186383, y: 0.000018548217, z: 0.007330766, w: 0.9999731}
  m_LocalPosition: {x: 0.0000007487835, y: 0.28141713, z: 0.0000009151411}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9031080444131072385}
  m_Father: {fileID: 8215425578896893511}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &8507390164161050081
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 20886035627276422}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.075982586
  m_Height: 0.27391738
  m_Direction: 1
  m_Center: {x: -0.00000032421664, y: 0.12450803, z: 0.0000000035406056}
--- !u!54 &8507390164161050082
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 20886035627276422}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &8507390164161050083
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 20886035627276422}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 8215425578896893513}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -0.8402332
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 139.15976
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!4 &9031080444131072385
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6172228005745030767}
  serializedVersion: 2
  m_LocalRotation: {x: 0.102513105, y: -0.0019429321, z: 0.026160488, w: 0.9943857}
  m_LocalPosition: {x: 0.00000017648561, y: 0.24901599, z: 0.00000016253436}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8507390164161050080}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!136 &9031080444131072386
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6172228005745030767}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.07003571
  m_Height: 0.20543809
  m_Direction: 1
  m_Center: {x: -0.0000005289913, y: 0.09338101, z: -0.0000000018626454}
--- !u!54 &9031080444131072387
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6172228005745030767}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &9031080444131072388
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6172228005745030767}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 8507390164161050082}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 961739753}
  - {fileID: 203844589}
  - {fileID: 236533411}
  - {fileID: 2060465284}
  - {fileID: 1949235397}
