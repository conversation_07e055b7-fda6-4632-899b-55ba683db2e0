using KINEMATION.MagicBlend.Runtime;
using NewAnimancer;
using UnityEngine;
using UnityEngine.Playables;

/// <summary>
/// Debug script to help diagnose MagicBlend connection issues.
/// Shows detailed information about playable graph connections.
/// </summary>
public class MagicBlendConnectionDebugger : MonoBehaviour
{
    [Header("Components")]
    [SerializeField] private HybridAnimancerComponent animancer;
    [SerializeField] private MagicBlending magicBlending;
    
    [Header("Debug Settings")]
    [SerializeField] private bool showDebugInfo = true;
    [SerializeField] private bool logConnectionDetails = false;
    
    private void Start()
    {
        if (animancer == null)
            animancer = GetComponent<HybridAnimancerComponent>();
            
        if (magicBlending == null)
            magicBlending = GetComponent<MagicBlending>();
    }

    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.F1))
        {
            LogConnectionDetails();
        }
        
        if (Input.GetKeyDown(KeyCode.F2))
        {
            LogPlayableGraphStructure();
        }
    }

    private void LogConnectionDetails()
    {
        if (magicBlending == null)
        {
            Debug.LogWarning("[MagicBlendDebugger] No MagicBlending component found!");
            return;
        }

        Debug.Log("=== MagicBlend Connection Details ===");
        Debug.Log($"useAnimancerAsBaseInput: {magicBlending.useAnimancerAsBaseInput}");
        Debug.Log($"playableGraph.IsValid(): {magicBlending.playableGraph.IsValid()}");
        Debug.Log($"OutputPlayable.IsValid(): {magicBlending.OutputPlayable.IsValid()}");
        
        if (animancer != null)
        {
            Debug.Log($"Animancer Layers Count: {animancer.Layers.Count}");
            if (animancer.Layers.Count > 0)
            {
                var baseLayer = animancer.Layers[0];
                Debug.Log($"Base Layer Playable.IsValid(): {baseLayer.Playable.IsValid()}");
                Debug.Log($"Current State: {animancer.States.Current}");
            }
        }
    }

    private void LogPlayableGraphStructure()
    {
        if (magicBlending == null || !magicBlending.playableGraph.IsValid())
        {
            Debug.LogWarning("[MagicBlendDebugger] No valid playable graph found!");
            return;
        }

        var graph = magicBlending.playableGraph;
        Debug.Log("=== Playable Graph Structure ===");
        Debug.Log($"Graph Output Count: {graph.GetOutputCount()}");
        
        for (int i = 0; i < graph.GetOutputCount(); i++)
        {
            var output = graph.GetOutput(i);
            Debug.Log($"Output {i}: {output.GetPlayableOutputType()}, Valid: {output.IsOutputValid()}");
            
            if (output.IsOutputValid())
            {
                var sourcePlayable = output.GetSourcePlayable();
                Debug.Log($"  Source Playable: {sourcePlayable.GetPlayableType()}, Valid: {sourcePlayable.IsValid()}");
                
                if (sourcePlayable.IsValid())
                {
                    LogPlayableInputs(sourcePlayable, "  ");
                }
            }
        }
    }

    private void LogPlayableInputs(Playable playable, string indent)
    {
        if (!playable.IsValid()) return;
        
        int inputCount = playable.GetInputCount();
        Debug.Log($"{indent}Input Count: {inputCount}");
        
        for (int i = 0; i < inputCount; i++)
        {
            var input = playable.GetInput(i);
            float weight = playable.GetInputWeight(i);
            Debug.Log($"{indent}Input {i}: {input.GetPlayableType()}, Valid: {input.IsValid()}, Weight: {weight:F3}");
            
            // Don't recurse too deep to avoid spam
            if (indent.Length < 8 && input.IsValid())
            {
                LogPlayableInputs(input, indent + "  ");
            }
        }
    }

    private void OnGUI()
    {
        if (!showDebugInfo) return;
        
        GUILayout.BeginArea(new Rect(Screen.width - 350, 10, 340, 300));
        GUILayout.Label("MagicBlend Connection Debugger", GUI.skin.box);
        
        if (magicBlending != null)
        {
            GUILayout.Label($"Use Animancer Base: {magicBlending.useAnimancerAsBaseInput}");
            GUILayout.Label($"Graph Valid: {magicBlending.playableGraph.IsValid()}");
            GUILayout.Label($"Output Valid: {magicBlending.OutputPlayable.IsValid()}");
        }
        
        if (animancer != null)
        {
            GUILayout.Label($"Animancer Layers: {animancer.Layers.Count}");
            GUILayout.Label($"Current State: {(animancer.States.Current?.ToString() ?? "None")}");
            
            if (animancer.Layers.Count > 0)
            {
                var baseLayer = animancer.Layers[0];
                GUILayout.Label($"Base Layer Valid: {baseLayer.Playable.IsValid()}");
            }
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("F1 - Log Connection Details"))
        {
            LogConnectionDetails();
        }
        
        if (GUILayout.Button("F2 - Log Graph Structure"))
        {
            LogPlayableGraphStructure();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("Press F1/F2 or use buttons above");
        
        GUILayout.EndArea();
    }
}
