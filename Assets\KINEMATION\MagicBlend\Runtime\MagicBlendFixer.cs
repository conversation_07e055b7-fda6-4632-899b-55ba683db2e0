using UnityEngine;
using KINEMATION.MagicBlend.Runtime;

/// <summary>
/// This script automatically disables old MagicBlending components and provides warnings
/// </summary>
[System.Serializable]
public class MagicBlendFixer : MonoBehaviour
{
    [Header("Auto-Fix Settings")]
    [SerializeField] private bool autoDisableOldComponents = true;
    [SerializeField] private bool showWarnings = true;

    private void Start()
    {
        if (autoDisableOldComponents)
        {
            FixOldMagicBlendingComponents();
        }
    }

    private void FixOldMagicBlendingComponents()
    {
        // Find all MagicBlending components in the scene
        var magicBlendingComponents = FindObjectsOfType<MagicBlending>();
        
        foreach (var component in magicBlendingComponents)
        {
            if (component.enabled)
            {
                component.enabled = false;
                if (showWarnings)
                {
                    Debug.LogWarning($"[MagicBlendFixer] Disabled old MagicBlending component on {component.gameObject.name}. Use SimpleMagicBlendState instead.", component);
                }
            }
        }

        if (magicBlendingComponents.Length > 0 && showWarnings)
        {
            Debug.Log($"[MagicBlendFixer] Fixed {magicBlendingComponents.Length} old MagicBlending components. Use animancer.Play(magicBlendAsset) instead.");
        }
    }

    [ContextMenu("Fix All MagicBlending Components")]
    public void FixAllComponents()
    {
        FixOldMagicBlendingComponents();
    }
}
