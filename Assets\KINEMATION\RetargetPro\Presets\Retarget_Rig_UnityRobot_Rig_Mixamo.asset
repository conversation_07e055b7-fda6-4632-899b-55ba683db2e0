%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8976631314224614527
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: d18adf1b9b851b3469eabde17ba78039, type: 2}
  featureWeight: 1
  sourceChain: rightLeg
  targetChain: rightLeg
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 1
  effectorOffset: {x: 0, y: 0, z: 0}
  effectorOffsetSpace:
    name: 
    index: 0
    isVirtual: 0
--- !u!114 &-1469591763799820434
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6e0cf430a4479280dbe53e7c58bd9c, type: 3}
  m_Name: BasicRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: d18adf1b9b851b3469eabde17ba78039, type: 2}
  featureWeight: 1
  sourceChain: pelvis
  targetChain: pelvis
  scaleWeight: 1
  translationWeight: 1
  offset: {x: 0, y: 0, z: 0}
--- !u!114 &-102348882782658188
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: d18adf1b9b851b3469eabde17ba78039, type: 2}
  featureWeight: 1
  sourceChain: leftArm
  targetChain: leftArm
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 1
  effectorOffset: {x: 0, y: 0, z: 0}
  effectorOffsetSpace:
    name: 
    index: 0
    isVirtual: 0
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 12349bae48c61824bb8e519537ce28eb, type: 3}
  m_Name: Retarget_Rig_UnityRobot_Rig_Mixamo
  m_EditorClassIdentifier: 
  sourceCharacter: {fileID: 0}
  targetCharacter: {fileID: 0}
  sourcePose: {fileID: 0}
  targetPose: {fileID: 0}
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: d18adf1b9b851b3469eabde17ba78039, type: 2}
  retargetFeatures:
  - {fileID: -1469591763799820434}
  - {fileID: -8976631314224614527}
  - {fileID: 1165019175171096106}
  - {fileID: 1073523852649616834}
  - {fileID: 2721149713207293157}
  - {fileID: 6746047711204293641}
  - {fileID: -102348882782658188}
  - {fileID: 6627845733179364061}
  - {fileID: 420112705287721220}
--- !u!114 &420112705287721220
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6e0cf430a4479280dbe53e7c58bd9c, type: 3}
  m_Name: BasicRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: d18adf1b9b851b3469eabde17ba78039, type: 2}
  featureWeight: 1
  sourceChain: toes
  targetChain: toes
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
--- !u!114 &1073523852649616834
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6e0cf430a4479280dbe53e7c58bd9c, type: 3}
  m_Name: BasicRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: d18adf1b9b851b3469eabde17ba78039, type: 2}
  featureWeight: 1
  sourceChain: spine
  targetChain: spine
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
--- !u!114 &1165019175171096106
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: d18adf1b9b851b3469eabde17ba78039, type: 2}
  featureWeight: 1
  sourceChain: leftLeg
  targetChain: leftLeg
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 1
  effectorOffset: {x: 0, y: 0, z: 0}
  effectorOffsetSpace:
    name: 
    index: 0
    isVirtual: 0
--- !u!114 &2721149713207293157
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6e0cf430a4479280dbe53e7c58bd9c, type: 3}
  m_Name: BasicRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: d18adf1b9b851b3469eabde17ba78039, type: 2}
  featureWeight: 1
  sourceChain: neck
  targetChain: neck
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
--- !u!114 &6627845733179364061
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6e0cf430a4479280dbe53e7c58bd9c, type: 3}
  m_Name: BasicRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: d18adf1b9b851b3469eabde17ba78039, type: 2}
  featureWeight: 1
  sourceChain: handFingers
  targetChain: handFingers
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
--- !u!114 &6746047711204293641
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: d18adf1b9b851b3469eabde17ba78039, type: 2}
  featureWeight: 1
  sourceChain: rightArm
  targetChain: rightArm
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 1
  effectorOffset: {x: 0, y: 0, z: 0}
  effectorOffsetSpace:
    name: 
    index: 0
    isVirtual: 0
