﻿using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using System;
using System.Collections.Generic;
using System.Reflection;
using NewAnimancer.Examples.AnimatorControllers.GameKit;
using NewAnimancer;
using Object = UnityEngine.Object;

[CustomEditor(typeof(LocomotionState))]
public class CharacterStateEditor : UnityEditor.Editor
{
    private RuntimeAnimatorController _animatorController;
    private string _blendTreeName;
    private List<string> _mixerTransition2DNames;
    private int _selectedMixerTransition2DIndex;

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        LocomotionState state = (LocomotionState)target;

        _animatorController = (RuntimeAnimatorController)EditorGUILayout.ObjectField("Animator",
            _animatorController, typeof(RuntimeAnimatorController), true);
        
        if (_mixerTransition2DNames == null)
        {
            _mixerTransition2DNames = new List<string>();
            Type stateType = typeof(LocomotionState);
            FieldInfo[] fields = stateType.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            foreach (FieldInfo field in fields)
            {
                if (field.FieldType == typeof(MixerTransition2D))
                {
                    _mixerTransition2DNames.Add(field.Name);
                }
            }
        }

        if (_mixerTransition2DNames.Count > 0)
        {
            _selectedMixerTransition2DIndex = EditorGUILayout.Popup("Mixer Transition 2D", _selectedMixerTransition2DIndex, _mixerTransition2DNames.ToArray());
        }
        
        _blendTreeName = EditorGUILayout.TextField("Blend Tree Name", _blendTreeName);

        if (GUILayout.Button("Get and Fill Blend Tree"))
        {
            string selectedMixerTransition2DName = _mixerTransition2DNames[_selectedMixerTransition2DIndex];
            FillLocomotionMixer(state, _animatorController, _blendTreeName, selectedMixerTransition2DName);
        }
    }

    private void FillLocomotionMixer(LocomotionState state, RuntimeAnimatorController animatorController, string blendTreeName, string selectedMixerTransition2DName)
    {
        if (animatorController == null || string.IsNullOrEmpty(blendTreeName))
        {
            Debug.LogWarning("Animator or Blend Tree Name is not set.");
            return;
        }

        AnimatorController controller = animatorController as AnimatorController;
        if (controller == null)
        {
            Debug.LogError("Animator does not have a valid AnimatorController.");
            return;
        }

        bool blendTreeFound = false;

        foreach (AnimatorControllerLayer layer in controller.layers)
        {
            blendTreeFound = RecursivelyFindAndFillBlendTree(layer.stateMachine, state, blendTreeName, layer.name, selectedMixerTransition2DName);
            if (blendTreeFound)
                break;
        }

        if (!blendTreeFound)
        {
            Debug.LogError($"Blend Tree named '{blendTreeName}' not found.");
        }
    }

    private bool RecursivelyFindAndFillBlendTree(AnimatorStateMachine stateMachine, LocomotionState state, string blendTreeName, string currentPath, string selectedMixerTransition2DName)
    {
        foreach (var childState in stateMachine.states)
        {
            string path = $"{currentPath}/{childState.state.name}";
            if (childState.state.motion is BlendTree blendTree && childState.state.name.Equals(blendTreeName))
            {
                var animations = new Object[blendTree.children.Length];
                var speeds = new float[blendTree.children.Length];
                var thresholds = new Vector2[blendTree.children.Length];
                for (int i = 0; i < blendTree.children.Length; i++)
                {
                    animations[i] = blendTree.children[i].motion as AnimationClip;
                    speeds[i] = blendTree.children[i].timeScale; // Use the actual speed from BlendTree's timeScale
                    thresholds[i] = new Vector2(blendTree.children[i].position.x, blendTree.children[i].position.y);
                    DebugLogManager.Instance.Log($"Found animation clip: {animations[i]?.name} at path: {path}/BlendTreeChild{i}");
                }

                SetMixerTransition2DField(state, selectedMixerTransition2DName, animations,thresholds, speeds);

                DebugLogManager.Instance.Log($"Filled {blendTreeName} with {blendTree.children.Length} animations at path: {path}.");
                return true; // Found and filled the blend tree
            }
        }

        foreach (var childStateMachine in stateMachine.stateMachines)
        {
            string stateMachinePath = $"{currentPath}/{childStateMachine.stateMachine.name}";
            if (RecursivelyFindAndFillBlendTree(childStateMachine.stateMachine, state, blendTreeName, stateMachinePath, selectedMixerTransition2DName))
            {
                return true;
            }
        }

        return false; // Blend tree not found in the current state machine
    }

    private void SetMixerTransition2DField(LocomotionState state, string fieldName, Object[] animations,Vector2[] thresholds, float[] speeds)
    {
        FieldInfo field = state.GetType().GetField(fieldName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
        if (field != null && field.FieldType == typeof(MixerTransition2D))
        {
            MixerTransition2D mixer = (MixerTransition2D)field.GetValue(state);
            mixer.Animations = animations;
            mixer.Speeds = speeds;
            mixer.Thresholds = thresholds;
            mixer.InitializeState();
        }
        else
        {
            Debug.LogError($"Field {fieldName} was not found or is not of type MixerTransition2D.");
        }
    }
}