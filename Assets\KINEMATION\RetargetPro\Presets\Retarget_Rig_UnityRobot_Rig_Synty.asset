%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8852642002934920575
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6e0cf430a4479280dbe53e7c58bd9c, type: 3}
  m_Name: BasicRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: 7bec62505f862604cabd6b9700ea385a, type: 2}
  featureWeight: 1
  sourceChain: pelvis
  targetChain: pelvis
  scaleWeight: 1
  translationWeight: 1
  offset: {x: 0, y: 0, z: 0}
--- !u!114 &-6762467602929046069
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6e0cf430a4479280dbe53e7c58bd9c, type: 3}
  m_Name: BasicRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: 7bec62505f862604cabd6b9700ea385a, type: 2}
  featureWeight: 1
  sourceChain: thumbFingers
  targetChain: thumbFingers
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
--- !u!114 &-3278943780676785490
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6e0cf430a4479280dbe53e7c58bd9c, type: 3}
  m_Name: BasicRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: 7bec62505f862604cabd6b9700ea385a, type: 2}
  featureWeight: 1
  sourceChain: indexFingers
  targetChain: indexFingers
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
--- !u!114 &-2356349348390401569
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: 7bec62505f862604cabd6b9700ea385a, type: 2}
  featureWeight: 1
  sourceChain: rightArm
  targetChain: rightArm
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 1
  effectorOffset: {x: 0, y: 0, z: 0}
  effectorOffsetSpace:
    name: 
    index: 0
    isVirtual: 0
--- !u!114 &-1433415314750269210
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6e0cf430a4479280dbe53e7c58bd9c, type: 3}
  m_Name: BasicRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: 7bec62505f862604cabd6b9700ea385a, type: 2}
  featureWeight: 1
  sourceChain: spine
  targetChain: spine
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 12349bae48c61824bb8e519537ce28eb, type: 3}
  m_Name: Retarget_Rig_UnityRobot_Rig_Synty
  m_EditorClassIdentifier: 
  sourceCharacter: {fileID: 0}
  targetCharacter: {fileID: 0}
  sourcePose: {fileID: 0}
  targetPose: {fileID: 0}
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: 7bec62505f862604cabd6b9700ea385a, type: 2}
  excludeChain:
    chainName: Elements To Exclude
    elementChain: []
    isStandalone: 1
  retargetFeatures:
  - {fileID: -8852642002934920575}
  - {fileID: 6813170467923900537}
  - {fileID: 2403607393580668804}
  - {fileID: -1433415314750269210}
  - {fileID: 980579327532453260}
  - {fileID: -2356349348390401569}
  - {fileID: 4092023143172689439}
  - {fileID: 1954448100663287235}
  - {fileID: -3278943780676785490}
  - {fileID: -6762467602929046069}
--- !u!114 &980579327532453260
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6e0cf430a4479280dbe53e7c58bd9c, type: 3}
  m_Name: BasicRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: 7bec62505f862604cabd6b9700ea385a, type: 2}
  featureWeight: 1
  sourceChain: neck
  targetChain: neck
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
--- !u!114 &1954448100663287235
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a6e0cf430a4479280dbe53e7c58bd9c, type: 3}
  m_Name: BasicRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: 7bec62505f862604cabd6b9700ea385a, type: 2}
  featureWeight: 1
  sourceChain: toes
  targetChain: toes
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
--- !u!114 &2403607393580668804
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: 7bec62505f862604cabd6b9700ea385a, type: 2}
  featureWeight: 1
  sourceChain: leftLeg
  targetChain: leftLeg
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 1
  effectorOffset: {x: 0, y: 0, z: 0}
  effectorOffsetSpace:
    name: 
    index: 0
    isVirtual: 0
--- !u!114 &4092023143172689439
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: 7bec62505f862604cabd6b9700ea385a, type: 2}
  featureWeight: 1
  sourceChain: leftArm
  targetChain: leftArm
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 1
  effectorOffset: {x: 0, y: 0, z: 0}
  effectorOffsetSpace:
    name: 
    index: 0
    isVirtual: 0
--- !u!114 &6813170467923900537
MonoBehaviour:
  m_ObjectHideFlags: 3
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9c037f4882c5493a84c1732694149815, type: 3}
  m_Name: IKRetargetFeature
  m_EditorClassIdentifier: 
  sourceRig: {fileID: 11400000, guid: d76ba7357ab72d445b9e1ff901bcd8c7, type: 2}
  targetRig: {fileID: 11400000, guid: 7bec62505f862604cabd6b9700ea385a, type: 2}
  featureWeight: 1
  sourceChain: rightLeg
  targetChain: rightLeg
  scaleWeight: 1
  translationWeight: 0
  offset: {x: 0, y: 0, z: 0}
  ikWeight: 1
  effectorOffset: {x: 0, y: 0, z: 0}
  effectorOffsetSpace:
    name: 
    index: 0
    isVirtual: 0
