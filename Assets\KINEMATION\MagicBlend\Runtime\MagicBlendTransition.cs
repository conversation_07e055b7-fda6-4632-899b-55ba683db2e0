// MagicBlendTransition - ScriptableObject for Animancer integration
// -----------------------------------------------------------------------------

#if UNITY_EDITOR || UNITY_STANDALONE || UNITY_ANDROID || UNITY_IOS
using System.Collections.Generic;
using KINEMATION.MagicBlend.Runtime;
using NewAnimancer;
using UnityEngine;

namespace MagicBlendAnimancerIntegration
{
    /// <summary>
    /// A ScriptableObject that wraps a <see cref="MagicBlendAsset"/> so it can be played via Animancer.
    /// You can create it from the Unity Create menu:  Create/Magic Blend/Transition
    /// </summary>
    [CreateAssetMenu(menuName = "Magic Blend/Transition", fileName = "MagicBlendTransition", order = 100)]
    [System.Serializable]
    public sealed class MagicBlendTransition : ScriptableObject, ITransition<MagicBlendState>, IAnimationClipSource
    {
        [SerializeField, Tooltip("The MagicBlendAsset to play")]
        private MagicBlendAsset _asset;

        [SerializeField, Tooltip("Default fade duration in seconds")]
        private float _fadeDuration = 0.25f;

        [SerializeField, Tooltip("Whether to use MagicBlend's internal blending or let Animancer handle fading")]
        private bool _useMagicBlendInternalFading = false;

        /// <summary>Gets or sets the MagicBlendAsset to play.</summary>
        public MagicBlendAsset Asset
        {
            get => _asset;
            set => _asset = value;
        }

        /// <summary>Gets or sets whether to use MagicBlend's internal blending.</summary>
        public bool UseMagicBlendInternalFading
        {
            get => _useMagicBlendInternalFading;
            set => _useMagicBlendInternalFading = value;
        }

        // ITransition implementation
        public float MaximumDuration => _asset?.isAnimation == true ? float.PositiveInfinity : (_asset?.blendTime ?? 0.15f);
        public float FadeDuration
        {
            get => _fadeDuration;
            set => _fadeDuration = value;
        }
        public bool IsValid => _asset != null;
        public object Key => (object)_asset ?? this; // Cast to object to avoid type mismatch
        public FadeMode FadeMode => FadeMode.FixedSpeed;

        // ITransition<MagicBlendState> implementation
        public MagicBlendState State { get; private set; }

        public MagicBlendState CreateState()
        {
            State = new MagicBlendState(_asset);
            return State;
        }

        AnimancerState ITransition.CreateState() => CreateState();

        public void Apply(AnimancerState state)
        {
            if (state is MagicBlendState magicState)
            {
                State = magicState;

                if (_useMagicBlendInternalFading)
                {
                    // If using MagicBlend's internal fading, update the asset with blending enabled
                    magicState.Blending?.UpdateMagicBlendAsset(_asset, true, _fadeDuration);
                }
            }
        }

        // Clone method for compatibility
        public MagicBlendTransition Clone()
        {
            var clone = ScriptableObject.CreateInstance<MagicBlendTransition>();
            clone._asset = _asset;
            clone._fadeDuration = _fadeDuration;
            clone._useMagicBlendInternalFading = _useMagicBlendInternalFading;
            return clone;
        }

        #region IAnimationClipSource
        public void GetAnimationClips(List<AnimationClip> clips)
        {
            if (_asset != null)
            {
                if (_asset.basePose != null) clips.Add(_asset.basePose);
                if (_asset.overlayPose != null) clips.Add(_asset.overlayPose);
            }
        }
        #endregion

#if UNITY_EDITOR
        // Enhanced inspector for MagicBlendTransition
        [UnityEditor.CustomEditor(typeof(MagicBlendTransition))]
        private class Editor : UnityEditor.Editor
        {
            public override void OnInspectorGUI()
            {
                serializedObject.Update();

                UnityEditor.EditorGUILayout.PropertyField(serializedObject.FindProperty("_asset"));
                UnityEditor.EditorGUILayout.PropertyField(serializedObject.FindProperty("_fadeDuration"));
                UnityEditor.EditorGUILayout.PropertyField(serializedObject.FindProperty("_useMagicBlendInternalFading"));

                // Show helpful information
                var asset = serializedObject.FindProperty("_asset").objectReferenceValue as MagicBlendAsset;
                if (asset != null)
                {
                    UnityEditor.EditorGUILayout.Space();
                    UnityEditor.EditorGUILayout.LabelField("Asset Info", UnityEditor.EditorStyles.boldLabel);
                    UnityEditor.EditorGUILayout.LabelField($"Base Pose: {(asset.basePose ? asset.basePose.name : "None")}");
                    UnityEditor.EditorGUILayout.LabelField($"Overlay Pose: {(asset.overlayPose ? asset.overlayPose.name : "None")}");
                    UnityEditor.EditorGUILayout.LabelField($"Is Animation: {asset.isAnimation}");
                    UnityEditor.EditorGUILayout.LabelField($"Blend Time: {asset.blendTime:F2}s");
                }

                serializedObject.ApplyModifiedProperties();
            }
        }
#endif
    }
}
#endif
