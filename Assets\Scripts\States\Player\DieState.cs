// Animancer // https://kybernetik.com.au/animancer // Copyright 2018-2023 Kybernetik //

#pragma warning disable CS0649 // Field is never assigned to, and will always have its default value.

using Module.Mono.Animancer.RealsticFemale;
using NewAnimancer;
using UnityEngine;
using UnityEvent = UnityEngine.Events.UnityEvent;

namespace NewAnimancer.Examples.AnimatorControllers.GameKit
{
    /// <summary>A <see cref="CharacterState"/> which plays a "dying" animation.</summary>
    /// <example><see href="https://kybernetik.com.au/animancer/docs/examples/animator-controllers/3d-game-kit/die">3D Game Kit/Die</see></example>
    /// https://kybernetik.com.au/animancer/api/Animancer.Examples.AnimatorControllers.GameKit/DieState
    /// 
    public sealed class DieState : CharacterState
    {
        /************************************************************************************************************************/

        [SerializeField] private ClipTransition _Animation;
        [SerializeField] private CharacterState _RespawnState;
        [SerializeField] private UnityEvent _OnEnterState;// See the Read Me.
        [SerializeField] private UnityEvent _OnExitState;// See the Read Me.

        /************************************************************************************************************************/

        private void Awake()
        {
            // Respawn immediately when the animation ends.
            //_Animation.Events.OnEnd = _RespawnState.ForceEnterState;
        }

        /************************************************************************************************************************/

        public void OnDeath()
        {
            Character.StateMachine.ForceSetState(this);
        }

        /************************************************************************************************************************/

        private void OnEnable()
        {
            Character.Animancer.Play(_Animation);
            Character.Parameters.InputMagnitude.Value = 0;
            _OnEnterState.Invoke();
        }

        /************************************************************************************************************************/

        private void OnDisable()
        {
            _OnExitState.Invoke();
        }

        /************************************************************************************************************************/

        public bool FullMovementControl => false;

        public override void OnEnterState(float fadeTime,System.Object conditions = null)
        {
            throw new System.NotImplementedException();
        }

        public override void UpdateState()
        {
            throw new System.NotImplementedException();
        }

        /************************************************************************************************************************/

        public override bool CanExitState => false;

        /************************************************************************************************************************/
    }
}
