%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5a8877f26e7a6a43aaf06fade1a064a, type: 3}
  m_Name: NormalWalkingBlendTree
  m_EditorClassIdentifier: 
  _Transition:
    rid: 1809481953731150415
  references:
    version: 2
    RefIds:
    - rid: 1809481953731150415
      type: {class: MixerTransition2D, ns: Animancer, asm: Kybernetik.Animancer}
      data:
        _FadeDuration: 0.25
        _Speed: 1
        _Events:
          _NormalizedTimes: []
          _Callbacks: []
          _Names: []
        _Animations:
        - {fileID: 7400034, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
        - {fileID: 11400000, guid: c722cdd5435cd99418beacdddd32831b, type: 2}
        - {fileID: 7400032, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
        - {fileID: 7400126, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
        - {fileID: 7400122, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
        - {fileID: 11400000, guid: 5b5b8da15b23b92458c92f69f53fe8ef, type: 2}
        _Speeds: []
        _SynchronizeChildren: 
        _Thresholds:
        - {x: -35, y: 0.7}
        - {x: 0, y: 0.7}
        - {x: 35, y: 0.7}
        - {x: -35, y: 1}
        - {x: 35, y: 1}
        - {x: 0, y: 1}
        _DefaultParameter: {x: 0, y: 0}
        _Type: 0
        _ParameterNameX: {fileID: 11400000, guid: 36225bd5a095e4d4d8845df97224f22f,
          type: 2}
        _ParameterNameY: {fileID: 11400000, guid: 898d71e680881324789f89a9159b331b,
          type: 2}
