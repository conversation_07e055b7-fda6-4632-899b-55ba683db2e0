// Enhanced integration between KINEMATION MagicBlend and Animancer
// Provides extension methods for direct MagicBlendAsset playback
// -----------------------------------------------------------------------------

#if UNITY_EDITOR || UNITY_STANDALONE || UNITY_ANDROID || UNITY_IOS
using KINEMATION.MagicBlend.Runtime;
using NewAnimancer;
using UnityEngine;

namespace MagicBlendAnimancerIntegration
{
    /// <summary>
    /// Extension methods to enable direct MagicBlendAsset playback through Animancer.
    /// Allows usage like: animancer.Play(magicBlendAsset, fadeTime);
    /// </summary>
    public static class AnimancerMagicBlendExtensions
    {
        /// <summary>
        /// Plays a MagicBlendAsset directly through Animancer.
        /// Uses the asset itself as the key for state management.
        /// </summary>
        /// <param name="animancer">The Animancer component</param>
        /// <param name="asset">The MagicBlendAsset to play</param>
        /// <param name="fadeDuration">Fade duration in seconds</param>
        /// <param name="mode">Fade mode</param>
        /// <returns>The created MagicBlendState</returns>
        public static MagicBlendState Play(this AnimancerComponent animancer, MagicBlendAsset asset, float fadeDuration = 0.25f, FadeMode mode = default)
        {
            if (asset == null)
            {
                Debug.LogError("[AnimancerMagicBlendExtensions] Cannot play null MagicBlendAsset");
                return null;
            }

            // Try to get existing state first
            if (animancer.States.TryGet(asset, out var existingState) && existingState is MagicBlendState existingMagicState)
            {
                // Play the existing state
                return animancer.Play(existingMagicState, fadeDuration, mode) as MagicBlendState;
            }

            // Create new state and register it properly
            var newState = new MagicBlendState(asset);

            // Set the graph first, then the key - this is the correct order
            newState.Key = asset;
            newState.SetGraph(animancer.Graph);

            

            // Play the new state
            return animancer.Play(newState, fadeDuration, mode) as MagicBlendState;
        }

        /// <summary>
        /// Plays a MagicBlendAsset on a specific Animancer layer.
        /// Uses the asset itself as the key for state management.
        /// </summary>
        /// <param name="layer">The Animancer layer</param>
        /// <param name="asset">The MagicBlendAsset to play</param>
        /// <param name="fadeDuration">Fade duration in seconds</param>
        /// <param name="mode">Fade mode</param>
        /// <returns>The created MagicBlendState</returns>
        public static MagicBlendState Play(this AnimancerLayer layer, MagicBlendAsset asset, float fadeDuration = 0.25f, FadeMode mode = default)
        {
            if (asset == null)
            {
                Debug.LogError("[AnimancerMagicBlendExtensions] Cannot play null MagicBlendAsset");
                return null;
            }

            // Try to get existing state first
            object key = asset;
            var existingState = layer.GetState(ref key) as MagicBlendState;
            if (existingState != null)
            {
                // Play the existing state
                return layer.Play(existingMagicState, fadeDuration, mode) as MagicBlendState;
            }

            // Create new state and register it properly
            var newState = new MagicBlendState(asset);

            // Set the graph first, then the key - this is the correct order
            newState.Key = asset;
            newState.SetGraph(layer.Graph);

            

            // Play the new state
            return layer.Play(newState, fadeDuration, mode) as MagicBlendState;
        }

        /// <summary>
        /// Tries to play a MagicBlendAsset if it exists in the state dictionary.
        /// </summary>
        /// <param name="animancer">The Animancer component</param>
        /// <param name="asset">The MagicBlendAsset to play</param>
        /// <param name="fadeDuration">Fade duration in seconds</param>
        /// <param name="mode">Fade mode</param>
        /// <returns>The MagicBlendState if found and played, null otherwise</returns>
        public static MagicBlendState TryPlay(this AnimancerComponent animancer, MagicBlendAsset asset, float fadeDuration = 0.25f, FadeMode mode = default)
        {
            if (asset == null) return null;

            // Try to get existing state
            if (animancer.States.TryGet(asset, out var existingState) && existingState is MagicBlendState magicState)
            {
                return animancer.Play(magicState, fadeDuration, mode) as MagicBlendState;
            }

            // If not found, create new one
            return Play(animancer, asset, fadeDuration, mode);
        }

        /// <summary>
        /// Gets or creates a MagicBlendState for the given asset without playing it.
        /// </summary>
        /// <param name="animancer">The Animancer component</param>
        /// <param name="asset">The MagicBlendAsset</param>
        /// <returns>The MagicBlendState</returns>
        public static MagicBlendState GetOrCreate(this AnimancerComponent animancer, MagicBlendAsset asset)
        {
            if (asset == null) return null;

            // Try to get existing state
            if (animancer.States.TryGet(asset, out var existingState) && existingState is MagicBlendState magicState)
            {
                return magicState;
            }

            // Create new state and register it properly
            var newState = new MagicBlendState(asset);
            newState.Key = asset;
            newState.SetGraph(animancer.Graph);
            return newState;
        }

        /// <summary>
        /// Stops a MagicBlendAsset if it's currently playing.
        /// </summary>
        /// <param name="animancer">The Animancer component</param>
        /// <param name="asset">The MagicBlendAsset to stop</param>
        /// <returns>The stopped state, or null if not found</returns>
        public static MagicBlendState Stop(this AnimancerComponent animancer, MagicBlendAsset asset)
        {
            if (asset == null) return null;

            if (animancer.States.TryGet(asset, out var state) && state is MagicBlendState magicState)
            {
                magicState.Stop();
                return magicState;
            }

            return null;
        }
    }

    /// <summary>
    /// Extension methods for AnimancerState to work with MagicBlend states
    /// </summary>
    public static class AnimancerStateExtensions
    {
        /// <summary>
        /// Checks if the AnimancerState is a MagicBlendState
        /// </summary>
        /// <param name="state">The AnimancerState to check</param>
        /// <returns>True if the state is a MagicBlendState, false otherwise</returns>
        public static bool IsMagicBlendState(this AnimancerState state)
        {
            return state is MagicBlendState;
        }

        /// <summary>
        /// Casts the AnimancerState to a MagicBlendState if possible
        /// </summary>
        /// <param name="state">The AnimancerState to cast</param>
        /// <returns>The MagicBlendState if the cast is successful, null otherwise</returns>
        public static MagicBlendState AsMagicBlendState(this AnimancerState state)
        {
            return state as MagicBlendState;
        }
    }

    /// <summary>
    /// Extension methods for MagicBlendAsset to create transitions and work with Animancer
    /// </summary>
    public static class MagicBlendAssetAnimancerExtensions
    {
        /// <summary>
        /// Creates a MagicBlendTransition from this MagicBlendAsset
        /// </summary>
        /// <param name="asset">The MagicBlendAsset</param>
        /// <param name="fadeTime">The fade duration for the transition</param>
        /// <returns>A new MagicBlendTransition</returns>
        public static MagicBlendTransition CreateTransition(this MagicBlendAsset asset, float fadeTime = 0.25f)
        {
            if (asset == null)
            {
                Debug.LogError("[MagicBlendAssetAnimancerExtensions] Cannot create transition from null MagicBlendAsset");
                return null;
            }

            var transition = ScriptableObject.CreateInstance<MagicBlendTransition>();
            transition.Asset = asset;
            transition.FadeDuration = fadeTime;

            // Set a name for debugging purposes
            transition.name = $"MagicBlendTransition_{asset.name}";

            return transition;
        }
    }
}
#endif
