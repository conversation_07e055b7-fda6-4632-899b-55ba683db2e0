%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a2d01d4f425b3848938f199392c9afb, type: 3}
  m_Name: NormalWalkingBlendTree
  m_EditorClassIdentifier: 
  _Transition:
    rid: 1809481966888157448
  references:
    version: 2
    RefIds:
    - rid: 1809481966888157448
      type: {class: MixerTransition2D, ns: Animancer, asm: Animancer}
      data:
        _FadeDuration: 0.22674449
        _Events:
          _NormalizedTimes:
          - 0.9365542
          _Callbacks: []
          _Names: []
        _Speed: 1.17
        _Animations:
        - {fileID: 7400000, guid: ba4bef9a13480ba4faf511bdef775c28, type: 2}
        - {fileID: 11400000, guid: 24d15d763b77c254e97c108f147dd8a4, type: 2}
        - {fileID: 7400000, guid: d9c48599be81af940b90e991a9a4d598, type: 2}
        - {fileID: 7400000, guid: d4d1b80d873c79542b7bbf3ecde07de3, type: 2}
        - {fileID: 7400000, guid: 17213978844bb074d9c98b5d3628671a, type: 2}
        - {fileID: 11400000, guid: 9ad339d49410e2748adb632577f8f95e, type: 2}
        _Speeds: []
        _SynchronizeChildren: 
        _Thresholds:
        - {x: -35, y: 0.7}
        - {x: 0, y: 0.7}
        - {x: 35, y: 0.7}
        - {x: -35, y: 1}
        - {x: 35, y: 1}
        - {x: 0, y: 1}
        _DefaultParameter: {x: 0, y: 0}
        _Type: 0
