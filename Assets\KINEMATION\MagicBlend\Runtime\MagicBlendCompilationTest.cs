// Compilation test for MagicBlend + Animancer integration
// This file tests that all the extension methods are working correctly

using UnityEngine;
using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;
using MagicBlendAnimancerIntegration;

namespace KINEMATION.MagicBlend.Runtime
{
    /// <summary>
    /// Simple compilation test to verify all extension methods are available
    /// </summary>
    public class MagicBlendCompilationTest : MonoBehaviour
    {
        [SerializeField] private AnimancerComponent _animancer;
        [SerializeField] private MagicBlendAsset _testAsset;

        private void Start()
        {
            TestCompilation();
        }

        private void TestCompilation()
        {
            if (_animancer == null || _testAsset == null)
            {
                Debug.LogWarning("Animancer or test asset is null, skipping tests");
                return;
            }

            try
            {
                Debug.Log("🧪 Starting MagicBlend integration tests...");

                // Test 1: Direct MagicBlendAsset playback (should work now)
                Debug.Log("Test 1: Direct asset playback");
                var state1 = _animancer.Play(_testAsset, 0.25f);
                Debug.Log($"✅ Direct asset play: {state1} (Key: {state1?.Key})");

                // Test 2: MagicBlendAsset.CreateTransition() (should work now)
                Debug.Log("Test 2: CreateTransition");
                var transition = _testAsset.CreateTransition(0.5f);
                Debug.Log($"✅ CreateTransition: {transition} (Key: {transition?.Key})");

                // Test 3: AnimancerState.IsMagicBlendState() (should work now)
                Debug.Log("Test 3: State type checking");
                var currentState = _animancer.States.Current;
                if (currentState != null)
                {
                    bool isMagicBlend = currentState.IsMagicBlendState();
                    Debug.Log($"✅ IsMagicBlendState: {isMagicBlend}");

                    // Test 4: AnimancerState.AsMagicBlendState() (should work now)
                    var magicBlendState = currentState.AsMagicBlendState();
                    Debug.Log($"✅ AsMagicBlendState: {magicBlendState}");
                }

                // Test 5: Play with FadeMode
                Debug.Log("Test 5: Play with FadeMode");
                var state2 = _animancer.Play(_testAsset, 0.25f, FadeMode.FixedSpeed);
                Debug.Log($"✅ Play with FadeMode: {state2}");

                // Test 6: Layer play
                Debug.Log("Test 6: Layer play");
                if (_animancer.Graph.Layers.Count > 0)
                {
                    var state3 = _animancer.Graph.Layers[0].Play(_testAsset, 0.25f);
                    Debug.Log($"✅ Layer play: {state3}");
                }

                Debug.Log("🎉 All MagicBlend + Animancer integration tests passed!");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Test failed: {ex.Message}\n{ex.StackTrace}");
            }
        }
    }
}
