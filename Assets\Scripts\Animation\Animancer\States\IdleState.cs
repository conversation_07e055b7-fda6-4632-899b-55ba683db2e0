using System.Collections.Generic;
using Events;
using NewAnimancer;
using NewAnimancer.Units;
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale
{
     public sealed class IdleState : CharacterState
    {
        /************************************************************************************************************************/

        [SerializeField] private ClipTransition _MainAnimation;
        [SerializeField, Seconds] private float _FirstRandomizeDelay = 5;
        [SerializeField, Seconds] private float _MinRandomizeInterval = 0;
        [SerializeField, Seconds] private float _MaxRandomizeInterval = 20;
        [SerializeField] private ClipTransition[] _normalAnimations;
        //[SerializeField] private LinearMixerTransitionAsset.UnShared m_inPlaceRotationBlendTree;

        [field:SerializeField] public Dictionary<WeaponSubModuleState,Dictionary<WeaponSubState,ClipTransition>> weaponsIdleAnimations { get; private set; }

        private float _RandomizeTime;

        [SerializeField] private float rotationSpeed = 10f;
        private Vector3? m_aimTarget;
        public override bool CanEnterState => Character.Parameters.IsGrounded;

        private void OnEnable()
        {
            EventManager.Subscribe<OnDetectTargetEvent>(OnDetectTarget);
            EventManager.Subscribe<OnLostTargetEvent>(OnLostTarget);

            // // Log blend tree setup for debugging
            // if (m_inPlaceRotationBlendTree != null)
            // {
            //     DebugLogManager.Instance.Log(
            //         $"[IdleState] BLEND TREE SETUP - Asset: {m_inPlaceRotationBlendTree.Asset.name}",
            //         DebugLogSettings.LogType.PlayerMovement);
            //
            //     // Initialize the blend tree to ensure it's ready for use
            //     try
            //     {
            //         // Force initialization by accessing the state
            //         var state = m_inPlaceRotationBlendTree.State;
            //         if (state != null)
            //         {
            //             // Play it once to ensure it's properly initialized
            //             if (Character != null && Character.Animancer != null)
            //             {
            //                 // Just initialize it without actually playing
            //                 Character.Animancer.States.GetOrCreate(m_inPlaceRotationBlendTree);
            //                 DebugLogManager.Instance.Log($"[IdleState] BLEND TREE INITIALIZED successfully in OnEnable", DebugLogSettings.LogType.PlayerMovement);
            //             }
            //             else
            //             {
            //                 DebugLogManager.Instance.Log($"[IdleState] WARNING: Character or Animancer is null, can't initialize blend tree", DebugLogSettings.LogType.PlayerMovement);
            //             }
            //         }
            //         else
            //         {
            //             DebugLogManager.Instance.Log($"[IdleState] WARNING: Blend tree state is null", DebugLogSettings.LogType.PlayerMovement);
            //         }
            //     }
            //     catch (System.Exception e)
            //     {
            //         DebugLogManager.Instance.Log($"[IdleState] ERROR initializing blend tree: {e.Message}", DebugLogSettings.LogType.PlayerMovement);
            //     }
            // }
        }

        private void OnDisable()
        {
            EventManager.Unsubscribe<OnDetectTargetEvent>(OnDetectTarget);
            EventManager.Unsubscribe<OnLostTargetEvent>(OnLostTarget);
        }

        private void OnDetectTarget(OnDetectTargetEvent onDetectTargetEvent)
        {
            m_aimTarget = onDetectTargetEvent.TargetPosition;
        }

        private void OnLostTarget(OnLostTargetEvent onLostTargetEvent)
        {
            m_aimTarget = null;
        }

        public override void OnEnterState(float fadeTime = 0,System.Object conditions = null)
        {
            if((WeaponSubModuleState)Character.Parameters.WeaponIndex == WeaponSubModuleState.EmptyHand)
                return;

            _RandomizeTime = UnityEngine.Random.Range(_MinRandomizeInterval, _MaxRandomizeInterval);

            // Initialize the blend tree to ensure it's ready for use
            // if (m_inPlaceRotationBlendTree != null)
            // {
            //     try
            //     {
            //         // Just initialize it without playing
            //         var state = m_inPlaceRotationBlendTree.State;
            //         if (state != null)
            //         {
            //             DebugLogManager.Instance.Log($"[IdleState] BLEND TREE INITIALIZED in OnEnterState", DebugLogSettings.LogType.PlayerMovement);
            //         }
            //     }
            //     catch (System.Exception e)
            //     {
            //         DebugLogManager.Instance.Log($"[IdleState] ERROR initializing blend tree in OnEnterState: {e.Message}", DebugLogSettings.LogType.PlayerMovement);
            //     }
            // }

            if(!Character.Parameters.IsAiming)
                Character.Animancer.Play(_MainAnimation,fadeTime);
            else
                Character.Animancer.Play(weaponsIdleAnimations[(WeaponSubModuleState)Character.Parameters.WeaponIndex]
                    [(WeaponSubState)GameManager.Instance.playerController.StateManager.GetSubState(typeof(WeaponSubState))],fadeTime);
        }

        public override void UpdateState()
        {
            // We use time where Mecanim used normalized time because choosing a number of seconds is much simpler than
            // finding out how long the animation is and working with multiples of that value.
            // if (Character.Animancer.States.Current.Time >= _RandomizeTime)
            // {
            //     PlayRandomAnimation();
            // }

            if(!Character.Parameters.IsAiming)
                Character.Animancer.Play(_MainAnimation);
            else if((WeaponSubModuleState)Character.Parameters.WeaponIndex != WeaponSubModuleState.EmptyHand)
                Character.Animancer.Play(weaponsIdleAnimations[(WeaponSubModuleState)Character.Parameters.WeaponIndex]
                    [(WeaponSubState)GameManager.Instance.playerController.StateManager.GetSubState(typeof(WeaponSubState))]);

            //RotateTowardsTarget();
        }

        /// <summary>
        /// Apply rotation through the in-place rotation blend tree
        /// </summary>
        /// <param name="rotateValue">Rotation value between -1 and 1, where -1 is full left rotation and 1 is full right rotation</param>
        private void InPlaceRotation(float rotateValue)
        {
            try
            {
                // Check if blend tree is null
                // if (m_inPlaceRotationBlendTree == null)
                // {
                //     DebugLogManager.Instance.Log($"[IdleState] ERROR - InPlaceRotationBlendTree is NULL!", DebugLogSettings.LogType.PlayerMovement);
                //     return;
                // }

                // Check if Character.Animancer is null
                if (Character == null || Character.Animancer == null)
                {
                    DebugLogManager.Instance.Log($"[IdleState] ERROR - Character or Animancer is null", DebugLogSettings.LogType.PlayerMovement);
                    return;
                }

                // Clamp the input value to valid range
                float clampedValue = Mathf.Clamp(rotateValue, -1f, 1f);
                
                // CRITICAL: Always play the blend tree FIRST to ensure it's active
                // This is essential for Animancer to properly apply the animation
                //Character.Animancer.Play(m_inPlaceRotationBlendTree, fadeDuration: 0.15f);
                
                // Get the state after playing to ensure it's initialized
                // var state = m_inPlaceRotationBlendTree.State;
                // if (state == null)
                // {
                //     DebugLogManager.Instance.Log($"[IdleState] ERROR - Blend tree state is NULL after play!", DebugLogSettings.LogType.PlayerMovement);
                //     return;
                // }
                
                // If stopping rotation (value is 0)
                // if (Mathf.Approximately(clampedValue, 0f))
                // {
                //     // Set parameter to exactly 0 for a clean stop
                //     state.Parameter = 0f;
                //     
                //     // After stopping rotation, transition back to idle animation
                //     Character.Animancer.Play(_MainAnimation, 0.25f);
                //     
                //     DebugLogManager.Instance.Log($"[IdleState] Rotation stopped, returning to idle animation", DebugLogSettings.LogType.PlayerMovement);
                // }
                // else
                // {
                //     // Set the parameter directly to the clamped value for immediate response
                //     state.Parameter = clampedValue;
                //     
                //     // Force evaluation to ensure the animation updates immediately
                //     Character.Animancer.Evaluate();
                //     
                //     DebugLogManager.Instance.Log($"[IdleState] Rotation active: value={clampedValue:F2}", DebugLogSettings.LogType.PlayerMovement);
                // }
            }
            catch (System.Exception e)
            {
                DebugLogManager.Instance.Log($"[IdleState] EXCEPTION in InPlaceRotation: {e.Message}", DebugLogSettings.LogType.PlayerMovement);
            }
        }

        /// <summary>
        /// Public method to apply in-place rotation from outside the class
        /// </summary>
        /// <param name="rotateAmount">Amount to rotate in degrees</param>
        public void ApplyInPlaceRotation(float rotateAmount)
        {
            InPlaceRotation(rotateAmount);
        }

        private void PlayRandomAnimation()
        {
            var index = UnityEngine.Random.Range(0, _normalAnimations.Length);
            var animation = _normalAnimations[index];
            Character.Animancer.Play(animation);
            //CustomFade.Apply(Character.Animancer, Easing.Function.SineInOut);
        }
    }
}