// // Animancer // https://kybernetik.com.au/animancer // Copyright 2018-2023 Kybernetik //
//
// #pragma warning disable CS0649 // Field is never assigned to, and will always have its default value.
//
// using Module.Mono.Animancer.RealsticFemale;
// using NewAnimancer;
// using NewAnimancer.Units;
// using UnityEngine;
// using UnityEvent = UnityEngine.Events.UnityEvent;
//
// namespace Animancer.Examples.AnimatorControllers.GameKit
// {
//     /// <summary>A <see cref="CharacterState"/> which plays an "airborne" animation.</summary>
//     /// <example><see href="https://kybernetik.com.au/animancer/docs/examples/animator-controllers/3d-game-kit/airborne">3D Game Kit/Airborne</see></example>
//     /// https://kybernetik.com.au/animancer/api/Animancer.Examples.AnimatorControllers.GameKit/AirborneState
//     /// 
//     public sealed class AirborneState : CharacterState
//     {
//         /************************************************************************************************************************/
//
//         [SerializeField] private LinearMixerTransition _Animations;
//         [SerializeField, MetersPerSecond] private float _JumpSpeed = 10;
//         [SerializeField, MetersPerSecond] private float _JumpAbortSpeed = 10;
//         [SerializeField, Multiplier] private float _TurnSpeedProportion = 5.4f;
//         [SerializeField] private LandingState _LandingState;
//         [SerializeField] private UnityEvent _PlayAudio;// See the Read Me.
//
//         private bool _IsJumping;
//
//         /************************************************************************************************************************/
//
//         private void OnEnable()
//         {
//             _IsJumping = false;
//             //StateMachines.Character.Animancer.Play(_Animations);
//         }
//
//         /************************************************************************************************************************/
//
//         public override void OnEnterState(float fadeTime,System.Object conditions = null)
//         {
//             throw new System.NotImplementedException();
//         }
//
//         /// <summary>
//         /// The airborne animations do not have root motion, so we just let the brain determine which way to go.
//         /// </summary>
//         
//         /************************************************************************************************************************/
//
//         public override void UpdateState()
//         {
//             // When you jump, do not start checking if you have landed until you stop going up.
//             if (_IsJumping)
//             {
//                 
//             }
//             else
//             {
//                 // If we have a landing state, try to enter it.
//                 if (_LandingState != null)
//                 {
//                     
//                 }
//                 else// Otherwise check the default transitions to Idle or Locomotion.
//                 {
//                     
//                 }
//
//             }
//
//
//             //Character.Movement.UpdateSpeedControl();
//
//             //var movement = Character.Parameters.MovementDirection;
//
//             // Since we do not have quick turn animations like the LocomotionState, we just increase the turn speed
//             // when the direction we want to go is further away from the direction we are currently facing.
//             
//         }
//
//         /************************************************************************************************************************/
//
//         public bool TryJump()
//         {
//             // We did not override CanEnterState to check if the Character is grounded because this state is also used
//             // if you walk off a ledge, so instead we check that condition here when specifically attempting to jump.
//
//             return false;
//         }
//
//         /************************************************************************************************************************/
//
//         public void CancelJump() => _IsJumping = false;
//
//         /************************************************************************************************************************/
//     }
// }
