%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-6830732958186206370
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5a8877f26e7a6a43aaf06fade1a064a, type: 3}
  m_Name: Pistol_Idle
  m_EditorClassIdentifier: 
  _Transition:
    rid: 1809481991901676127
  references:
    version: 2
    RefIds:
    - rid: 1809481991901676127
      type: {class: ClipTransition, ns: NewAnimancer, asm: Kybernetik.Animancer}
      data:
        _FadeDuration: 0.25
        _Speed: 1
        _Events:
          _NormalizedTimes: []
          _Callbacks: []
          _Names: []
        _Clip: {fileID: 7400000, guid: 21b11001721b1c147bcca3b2c4a09c7e, type: 2}
        _NormalizedStartTime: NaN
--- !u!114 &-6501324429565988989
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5a8877f26e7a6a43aaf06fade1a064a, type: 3}
  m_Name: Idle
  m_EditorClassIdentifier: 
  _Transition:
    rid: 1809481991901676128
  references:
    version: 2
    RefIds:
    - rid: 1809481991901676128
      type: {class: ClipTransition, ns: NewAnimancer, asm: Kybernetik.Animancer}
      data:
        _FadeDuration: 0.25
        _Speed: 1
        _Events:
          _NormalizedTimes: []
          _Callbacks: []
          _Names: []
        _Clip: {fileID: 7400002, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
        _NormalizedStartTime: NaN
--- !u!114 &-3986130371191061560
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5a8877f26e7a6a43aaf06fade1a064a, type: 3}
  m_Name: Pistol_Idle_Relaxed
  m_EditorClassIdentifier: 
  _Transition:
    rid: 1809481991901676129
  references:
    version: 2
    RefIds:
    - rid: 1809481991901676129
      type: {class: ClipTransition, ns: NewAnimancer, asm: Kybernetik.Animancer}
      data:
        _FadeDuration: 0.25
        _Speed: 1
        _Events:
          _NormalizedTimes: []
          _Callbacks: []
          _Names: []
        _Clip: {fileID: 7400000, guid: 37387e3558950f846884db1269793fed, type: 2}
        _NormalizedStartTime: NaN
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6f08fdd50e79d0c4bb235a5c2696516d, type: 3}
  m_Name: New Transition Library Asset
  m_EditorClassIdentifier: 
  _Definition:
    _Transitions:
    - {fileID: -6501324429565988989}
    - {fileID: -6830732958186206370}
    - {fileID: -3986130371191061560}
    - {fileID: 2450798413961996696}
    - {fileID: 11400000, guid: c072c0d394324a819098e4944ebd4dad, type: 2}
    - {fileID: 11400000, guid: 5389eab08cd04a4dbe2e8ffcaa0b6acf, type: 2}
    - {fileID: 11400000, guid: dd5aeec90af446ce9f4f2faefe3335dc, type: 2}
    - {fileID: 11400000, guid: 97cb67b58d734e36bfef1dd2d22f481e, type: 2}
    - {fileID: 11400000, guid: 27cd64ceb8d545c386ee38b4cdc74432, type: 2}
    - {fileID: 11400000, guid: 25b26a3f13fe43039117e9902617719e, type: 2}
    _Modifiers:
    - _From: 9
      _To: 4
      _Fade: 0.1
    - _From: 9
      _To: 5
      _Fade: 0.1
    - _From: 9
      _To: 6
      _Fade: 0.1
    - _From: 9
      _To: 7
      _Fade: 0.1
    - _From: 9
      _To: 8
      _Fade: 0.1
    - _From: 9
      _To: 9
      _Fade: 0.1
    - _From: 5
      _To: 5
      _Fade: 0.2
    - _From: 6
      _To: 5
      _Fade: 0.2
    - _From: 7
      _To: 5
      _Fade: 0.2
    - _From: 8
      _To: 5
      _Fade: 0.2
    - _From: 8
      _To: 6
      _Fade: 0.2
    - _From: 6
      _To: 6
      _Fade: 0.2
    - _From: 5
      _To: 6
      _Fade: 0.2
    - _From: 8
      _To: 8
      _Fade: 0.2
    - _From: 7
      _To: 8
      _Fade: 0.2
    - _From: 6
      _To: 8
      _Fade: 0.2
    - _From: 5
      _To: 8
      _Fade: 0.2
    - _From: 4
      _To: 8
      _Fade: 0.2
    - _From: 8
      _To: 9
      _Fade: 0.2
    - _From: 7
      _To: 9
      _Fade: 0.2
    - _From: 6
      _To: 9
      _Fade: 0.2
    - _From: 5
      _To: 9
      _Fade: 0.2
    - _From: 4
      _To: 9
      _Fade: 0.2
    - _From: 8
      _To: 7
      _Fade: 0.2
    - _From: 7
      _To: 7
      _Fade: 2
    - _From: 6
      _To: 7
      _Fade: 0.2
    - _From: 5
      _To: 7
      _Fade: 0.2
    - _From: 4
      _To: 7
      _Fade: 0.2
    - _From: 7
      _To: 6
      _Fade: 0.2
    - _From: 4
      _To: 0
      _Fade: 0.1
    - _From: 6
      _To: 0
      _Fade: 0.1
    - _From: 8
      _To: 0
      _Fade: 0
    - _From: 9
      _To: 0
      _Fade: 0
    - _From: 8
      _To: 2
      _Fade: 0
    - _From: 9
      _To: 2
      _Fade: 0
    - _From: 8
      _To: 1
      _Fade: 0
    - _From: 9
      _To: 1
      _Fade: 0
    - _From: 8
      _To: 3
      _Fade: 0
    - _From: 9
      _To: 3
      _Fade: 0
    - _From: 4
      _To: 2
      _Fade: 0.1
    - _From: 5
      _To: 1
      _Fade: 0.1
    - _From: 5
      _To: 3
      _Fade: 0.1
    - _From: 4
      _To: 1
      _Fade: 0.1
    - _From: 4
      _To: 3
      _Fade: 0.1
    - _From: 4
      _To: 5
      _Fade: 0.15
    - _From: 4
      _To: 6
      _Fade: 0.15
    - _From: 4
      _To: 4
      _Fade: 0.3
    _Aliases: []
    _AliasAllTransitions: 0
--- !u!114 &2450798413961996696
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5a8877f26e7a6a43aaf06fade1a064a, type: 3}
  m_Name: Aim MP-40 Standing Breathe
  m_EditorClassIdentifier: 
  _Transition:
    rid: 1809481991901676130
  references:
    version: 2
    RefIds:
    - rid: 1809481991901676130
      type: {class: ClipTransition, ns: NewAnimancer, asm: Kybernetik.Animancer}
      data:
        _FadeDuration: 0.25
        _Speed: 1
        _Events:
          _NormalizedTimes: []
          _Callbacks: []
          _Names: []
        _Clip: {fileID: 7400000, guid: 2eca9b0c87f01c843903863e6d00a38d, type: 2}
        _NormalizedStartTime: NaN
--- !u!114 &7764996403470143994
MonoBehaviour:
  m_ObjectHideFlags: 17
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f04d4a67566152e40aaa01b8b745ff6b, type: 3}
  m_Name: Editor Data
  m_EditorClassIdentifier: 
  _Library: {fileID: 11400000}
  _TransitionSortMode: 0
