// Animancer // https://kybernetik.com.au/animancer // Copyright 2018-2023 Kybernetik //

#pragma warning disable CS0649 // Field is never assigned to, and will always have its default value.
using System;
using NewAnimancer.Samples.Events;
using Events;
using UnityEngine;
using UnityEngine.Events;

namespace PlayerFAP._Mono
{
    /// <summary>
    /// A variation of the base <see cref="FootstepEvents"/> which responds to Animation Events called "Footstep" by
    /// playing a sound randomly selected from an array, using the Int Parameter of the event as the index to determine
    /// which foot to play the sound on. 0 is the Left Foot and 1 is the Right Foot.
    /// </summary>
    /// <example><see href="https://kybernetik.com.au/animancer/docs/examples/events/footsteps">Footstep Events</see></example>
    /// https://kybernetik.com.au/animancer/api/Animancer.Examples.Events/FootstepEventsAnimation
    ///
    public sealed class FootstepEventsAnimation : MonoBehaviour
    {
        /************************************************************************************************************************/
        [SerializeField] private AudioSource _audioSource;
        [SerializeField] private FootstepEvents _FootstepEvents;
        [SerializeField] private AudioSource[] _FootSources;
        [SerializeField] private float _baseAnimationSpeed = 1.0f;
        [SerializeField] private float _currentSpeedMultiplier = 1.0f;

        public UnityEvent OnLeftFootstepHit;
        public UnityEvent OnRightFootstepHit;
        /************************************************************************************************************************/

        // Called by Animation Events.
        private void Footstep(int foot)
        {
            _FootstepEvents.PlaySound(_FootSources[foot]);
        }

        public void LeftFootstep()
        {
            //_audioSource.Play();
            OnLeftFootstepHit?.Invoke();
            EventManager.Broadcast<OnFootStepEvent>(new OnFootStepEvent() {IsLeftFoot = true});
        }

        public void RightFootstep()
        {
            //_audioSource.Play();
            OnRightFootstepHit?.Invoke();
            EventManager.Broadcast<OnFootStepEvent>(new OnFootStepEvent() {IsLeftFoot = false});
        }

        /// <summary>
        /// Sets the speed multiplier for footstep animations.
        /// This affects how quickly footstep events are triggered based on movement speed.
        /// </summary>
        /// <param name="multiplier">The speed multiplier to apply (1.0 = normal speed)</param>
        public void SetSpeedMultiplier(float multiplier)
        {
            _currentSpeedMultiplier = Mathf.Clamp(multiplier, 0.5f, 2.0f);

            // Apply the speed multiplier to any animation components that need it
            // var animator = GetComponentInParent<Animator>();
            // if (animator != null)
            // {
            //     // Adjust the speed of footstep-related animations
            //     animator.SetFloat("FootstepSpeed", _currentSpeedMultiplier);
            // }

            // Adjust audio pitch based on speed if needed
            if (_FootSources != null)
            {
                foreach (var source in _FootSources)
                {
                    if (source != null)
                    {
                        // Adjust pitch slightly based on speed (but not too much to avoid sounding unnatural)
                        source.pitch = Mathf.Lerp(0.9f, 1.1f, (_currentSpeedMultiplier - 0.5f) / 1.5f);
                    }
                }
            }
        }

        /// <summary>
        /// Gets the current speed multiplier for footstep animations.
        /// </summary>
        public float GetSpeedMultiplier()
        {
            return _currentSpeedMultiplier;
        }

        /************************************************************************************************************************/
    }
}
