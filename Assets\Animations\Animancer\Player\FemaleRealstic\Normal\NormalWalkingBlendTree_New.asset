%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5a8877f26e7a6a43aaf06fade1a064a, type: 3}
  m_Name: NormalWalkingBlendTree_New
  m_EditorClassIdentifier: 
  _Transition:
    rid: 1809481973264810171
  references:
    version: 2
    RefIds:
    - rid: 1809481973264810171
      type: {class: MixerTransition2D, ns: NewAnimancer, asm: Kybernetik.Animancer}
      data:
        _FadeDuration: 0.22674449
        _Speed: 1.17
        _Events:
          _NormalizedTimes: []
          _Callbacks: []
          _Names: []
        _Animations:
        - {fileID: 7400000, guid: ba4bef9a13480ba4faf511bdef775c28, type: 2}
        - {fileID: 7400000, guid: a1002e6980b7b124ab44c287d8732f2d, type: 2}
        - {fileID: 7400000, guid: d9c48599be81af940b90e991a9a4d598, type: 2}
        - {fileID: 7400000, guid: d4d1b80d873c79542b7bbf3ecde07de3, type: 2}
        - {fileID: 7400000, guid: 17213978844bb074d9c98b5d3628671a, type: 2}
        - {fileID: 7400000, guid: 281fc932dafc8a94db0fde888c145bea, type: 2}
        _Speeds: []
        _SynchronizeChildren: 
        _Thresholds:
        - {x: -35, y: 0.7}
        - {x: 0, y: 0.7}
        - {x: 35, y: 0.7}
        - {x: -35, y: 1}
        - {x: 35, y: 1}
        - {x: 0, y: 1}
        _DefaultParameter: {x: 0, y: 0}
        _Type: 0
        _ParameterNameX: {fileID: 11400000, guid: 9e4817fd41ab4dc4eb874bc8caacac99,
          type: 2}
        _ParameterNameY: {fileID: 11400000, guid: d6f2743c321d4815b9cad95e6a42273f,
          type: 2}
