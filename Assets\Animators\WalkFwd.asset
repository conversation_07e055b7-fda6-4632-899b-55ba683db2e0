%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5a8877f26e7a6a43aaf06fade1a064a, type: 3}
  m_Name: WalkFwd
  m_EditorClassIdentifier: 
  _Transition:
    rid: 1809481953731150411
  references:
    version: 2
    RefIds:
    - rid: 1809481953731150411
      type: {class: LinearMixerTransition, ns: Animancer, asm: Kybernetik.Animancer}
      data:
        _FadeDuration: 0.25
        _Speed: 1
        _Events:
          _NormalizedTimes: []
          _Callbacks: []
          _Names: []
        _Animations:
        - {fileID: 7400146, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
        - {fileID: 7400016, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
        - {fileID: 7400144, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
        _Speeds:
        - 0.64215237
        - 1.020814
        - 2.159236
        _SynchronizeChildren: 
        _Thresholds:
        - -35
        - 0
        - 35
        _DefaultParameter: 0
        _ExtrapolateSpeed: 1
        _ParameterName: {fileID: 0}
