// Animancer // https://kybernetik.com.au/animancer // Copyright 2018-2023 Kybernetik //

#pragma warning disable CS0649 // Field is never assigned to, and will always have its default value.

using System;
using NewAnimancer;
using KINEMATION.MagicBlend.Runtime;
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale.States.Weapon.Mp4
{
    public sealed class Mp4AnimationState : WeaponState
    {
        public MagicBlendAsset Equip;
        public MagicBlendAsset UnEquip;
        public MagicBlendAsset Idle;
        public MagicBlendAsset Aiming;
        public MagicBlendAsset Shooting;  
        public MagicBlendAsset Reloading;
        public override void OnEnterState(float fadeTime, WeaponSubState weaponSubState,Action onEnd)
        {
            switch (weaponSubState)
            {
                case WeaponSubState.Equipping:
                    // if (onEnd != null)
                    //     Equip.Events.OnEnd = onEnd;
                    // Character.SetUpperBodyAnimation(Equip);
                    WeaponGameObject.SetActive(true);
                    break;
                case WeaponSubState.UnEquipping:
                    // if (onEnd != null)
                    //     UnEquip.Events.OnEnd = onEnd;
                    // Character.SetUpperBodyAnimation(UnEquip);
                    
                    break;
                case WeaponSubState.Idle:
                    Character.SetUpperBodyAnimation(Idle);
                    break;
                case WeaponSubState.Aiming:
                    Character.SetUpperBodyAnimation(Aiming);
                    break;
                case WeaponSubState.Reloading:
                    
                    break;
                case WeaponSubState.Shooting:
                    
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(weaponSubState), weaponSubState, null);
            }
        }

        public override void UpdateState(WeaponSubState weaponSubState)
        {
            DebugLogManager.Instance.Log("update Mp4Animation State");
            
        }
        
        public override void OnExitState()
        {
            WeaponGameObject.SetActive(false);
        }
    }
}
