%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1137305049, guid: a4865f1ab4504ed8a368670db22f409c, type: 3}
  m_Name: InspectorConfig
  m_EditorClassIdentifier: 
  enableOdinInInspector: 1
  defaultEditorBehaviour: 9
  processMouseMoveInInspector: 1
  drawingConfig:
    configs:
    - DrawnTypeName: RootMotion.AnimationModifierStack, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.CameraControllerFPS, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.DemoGUIMessage, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.SolverManager, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.TriggerEventBroadcaster, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.AnimatorIKDemo, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.ApplicationQuit, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.BallShooter, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.BoardController, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.CharacterAnimationMeleeDemo, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.CharacterAnimationSimple, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.CharacterAnimationThirdPerson, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.CharacterThirdPerson, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.CreatePuppetInRuntime, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.CreateRagdollInRuntime, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.DeathBaker, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.Destructor, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.Dying, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.FKOffset, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.FXCollisionBlood, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.Grab, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.Killing, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.LayerSetup, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.NavMeshPuppet, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.Planet, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.PlanetaryGravity, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.Platform, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.PropDemo, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.PropMelee, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.PropPickUpTrigger, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.PuppetBoard, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.PuppetScaling, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.RaycastShooter, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.Respawning, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.RotateShoulderToTarget, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.SimpleLocomotion, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.SkeletonDisconnector, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.SkeletonShooter, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.SlowMo, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.UserControlAI, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.UserControlAIMelee, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.UserControlMelee, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Demos.UserControlThirdPerson, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.Actuator, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.AnimationBlocker, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.BehaviourFall, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.BehaviourPuppet, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.BehaviourTemplate, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.CollisionEventBroadcaster, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.FixFootColliders, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.InitialVelocity, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.JointBreakBroadcaster, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.MuscleCollisionBroadcaster, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.ParticleCollisionHandler, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.PressureSensor, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.PropRoot, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.PropTemplate, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.PuppetControllerLite, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.PuppetMasterSettings, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: RootMotion.Dynamics.RigidbodyController, Assembly-CSharp-firstpass
      EditorTypeName: 
    - DrawnTypeName: Animancer.ExposedPropertyTable, Animancer
      EditorTypeName: 
    - DrawnTypeName: Animancer.RedirectRootMotionToCharacterController, Animancer
      EditorTypeName: 
    - DrawnTypeName: Animancer.RedirectRootMotionToRigidbody, Animancer
      EditorTypeName: 
    - DrawnTypeName: Animancer.RedirectRootMotionToTransform, Animancer
      EditorTypeName: 
    - DrawnTypeName: Animancer.SpriteRendererTextureSwap, Animancer
      EditorTypeName: 
    - DrawnTypeName: Animancer.Editor.AnimationClipPreview, Animancer
      EditorTypeName: 
    - DrawnTypeName: Animancer.Editor.DummySerializableCallback, Animancer
      EditorTypeName: 
    - DrawnTypeName: Animancer.Editor.TemporarySettings, Animancer
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.OrbitControls, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.TimeScale, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.AnimatorControllers.HybridBasics, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.AnimatorControllers.HybridIdleState, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.AnimatorControllers.HybridMoveState, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.AnimatorControllers.GameKit.AirborneState,
        Assembly-CSharp
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.AnimatorControllers.GameKit.FlinchState,
        Assembly-CSharp
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.AnimatorControllers.GameKit.LandingState,
        Assembly-CSharp
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.AnimatorControllers.GameKit.RespawnState,
        Assembly-CSharp
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Basics.BasicCharacterAnimations, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Basics.BasicMovementAnimations, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Basics.PlayAnimationOnClick, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Basics.PlayAnimationOnEnable, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Basics.PlayTransitionOnClick, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.DirectionalSprites.DirectionalBasics, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.DirectionalSprites.DirectionalCharacter,
        Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Events.FootstepEvents, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Events.FootstepEventsAnimation, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Events.Golfer, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.FineControl.ClickToInteract, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.FineControl.Door, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.FineControl.DynamicUpdateRate, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.FineControl.LowUpdateRate, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.FineControl.NamedAnimations, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.FineControl.SpiderBot, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.InverseKinematics.IKPuppet, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.InverseKinematics.IKPuppetLookTarget, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.InverseKinematics.IKPuppetTarget, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.InverseKinematics.MouseDrag, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.InverseKinematics.ObstacleTreadmill, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.InverseKinematics.RaycastFootIK, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.InverseKinematics.TransformResetter, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Jobs.Damping, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Jobs.SimpleLeanComponent, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Jobs.TwoBoneIK, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Layers.DynamicLayeredCharacterAnimations,
        Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Layers.LayeredAnimationManager, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Layers.LayeredCharacterAnimations, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Locomotion.LinearBlendTreeLocomotion, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Locomotion.LinearMixerLocomotion, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Locomotion.RootMotion, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Locomotion.ScreenBoundsTeleporter, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.Locomotion.SpiderBotController, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.ActionState, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.AttackState, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.BasicCharacterBrain, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.Character, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.Equipment, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.EquipState, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.FlinchState, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.HealthPool, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.IdleState, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.MoveState, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.MovingCharacterBrain, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.RootMotionRedirect, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.Weapon, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Examples.StateMachines.WeaponsCharacterBrain, Animancer.Examples
      EditorTypeName: 
    - DrawnTypeName: Animancer.Samples.Events.FootstepEvents, Assembly-CSharp
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.DirectionalAnimations3D, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.DirectionalAnimations3D`1, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.DirectionalAnimationSet2, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.DirectionalSet2`1, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.DirectionalSet4`1, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.DirectionalSet8`1, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.ExposedPropertyTable, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.RedirectRootMotionToCharacterController, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.RedirectRootMotionToRigidbody, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.RedirectRootMotionToTransform, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.SpriteRendererTextureSwap, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.WeightedMaskLayers, Kybernetik.Animancer
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.Editor.DummyInvokableDrawer, Kybernetik.Animancer.Editor
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.Editor.MaterialVariantGroup, Kybernetik.Animancer.Editor
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.Editor.TemporarySettings, Kybernetik.Animancer.Editor
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.Editor.Previews.AnimationClipPreview, Kybernetik.Animancer.Editor
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.Examples.AnimatorControllers.GameKit.AttackState,
        Assembly-CSharp
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.Examples.AnimatorControllers.GameKit.DieState,
        Assembly-CSharp
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.Examples.AnimatorControllers.GameKit.IdleState,
        Assembly-CSharp
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.Examples.AnimatorControllers.GameKit.ToStopState,
        Assembly-CSharp
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.Examples.AnimatorControllers.GameKit.FlinchState,
        Assembly-CSharp
      EditorTypeName: 
    - DrawnTypeName: NewAnimancer.Samples.Events.FootstepEvents, Assembly-CSharp
      EditorTypeName: 
